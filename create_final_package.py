#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إنشاء حزمة نهائية مضغوطة للبرنامج
Create final compressed package for the application
"""

import os
import shutil
import zipfile
import time
from pathlib import Path


def create_zip_package():
    """إنشاء ملف zip للحزمة الكاملة"""
    try:
        dist_dir = Path('dist')
        
        # البحث عن مجلد الحزمة
        package_dirs = [d for d in dist_dir.iterdir() if d.is_dir() and 'نظام_إدارة_المخازن_كامل' in d.name]
        
        if not package_dirs:
            print("❌ لم يتم العثور على مجلد الحزمة")
            return False
        
        # أخذ أحدث مجلد
        package_dir = max(package_dirs, key=lambda x: x.stat().st_mtime)
        
        # إنشاء اسم ملف zip
        timestamp = time.strftime('%Y%m%d_%H%M%S')
        zip_name = f"نظام_إدارة_المخازن_محمول_{timestamp}.zip"
        zip_path = dist_dir / zip_name
        
        print(f"📦 إنشاء ملف مضغوط: {zip_name}")
        
        # إنشاء ملف zip
        with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED, compresslevel=6) as zipf:
            for root, dirs, files in os.walk(package_dir):
                for file in files:
                    file_path = Path(root) / file
                    arc_name = file_path.relative_to(package_dir)
                    zipf.write(file_path, arc_name)
                    print(f"  ✅ إضافة: {arc_name}")
        
        # حساب حجم الملف
        zip_size = zip_path.stat().st_size / (1024 * 1024)
        
        print(f"\n✅ تم إنشاء الملف المضغوط بنجاح!")
        print(f"📁 المسار: {zip_path}")
        print(f"📊 حجم الملف: {zip_size:.2f} ميجابايت")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء الملف المضغوط: {e}")
        return False


def create_installer_info():
    """إنشاء ملف معلومات التثبيت"""
    try:
        dist_dir = Path('dist')
        info_file = dist_dir / 'معلومات_التثبيت.txt'
        
        info_content = f"""
# نظام إدارة المخازن والمستودعات
## معلومات التثبيت والاستخدام

### الملفات المتوفرة:
1. نظام_إدارة_المخازن_كامل.exe - ملف تنفيذي مستقل (48+ MB)
2. نظام_إدارة_المخازن_محمول_[التاريخ].zip - حزمة محمولة مضغوطة

### طريقة الاستخدام:

#### الخيار الأول: الملف التنفيذي المستقل
- انقر نقراً مزدوجاً على "نظام_إدارة_المخازن_كامل.exe"
- سيتم تشغيل البرنامج مباشرة
- جميع المكتبات مدمجة في الملف

#### الخيار الثاني: الحزمة المحمولة
- استخرج ملف ZIP إلى أي مجلد
- انقر نقراً مزدوجاً على "نظام_إدارة_المخازن.exe"
- يحتوي على جميع الملفات والمجلدات المطلوبة

### بيانات تسجيل الدخول الافتراضية:
- اسم المستخدم: admin
- كلمة المرور: admin

### المتطلبات:
- نظام التشغيل: Windows 10/11
- الذاكرة: 4 GB RAM (مُوصى به)
- مساحة القرص: 100 MB

### الميزات:
✅ إدارة المخازن والمستودعات
✅ إدارة الأصناف والمواد
✅ إدارة المستفيدين والمعاملات
✅ التقارير والإحصائيات
✅ النسخ الاحتياطية
✅ واجهة عربية كاملة

### الدعم الفني:
للحصول على الدعم الفني أو الإبلاغ عن مشاكل، يرجى التواصل مع فريق التطوير.

تاريخ الإنشاء: {time.strftime('%Y-%m-%d %H:%M:%S')}
إصدار البرنامج: 2.0
"""
        
        with open(info_file, 'w', encoding='utf-8') as f:
            f.write(info_content)
        
        print(f"✅ تم إنشاء ملف معلومات التثبيت: {info_file}")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء ملف المعلومات: {e}")
        return False


def main():
    """الدالة الرئيسية"""
    print("📦 إنشاء الحزمة النهائية للبرنامج")
    print("=" * 50)
    
    # التحقق من وجود مجلد dist
    if not Path('dist').exists():
        print("❌ مجلد dist غير موجود. يجب بناء البرنامج أولاً.")
        return False
    
    # إنشاء ملف zip
    if create_zip_package():
        print("\n" + "=" * 50)
        
        # إنشاء ملف معلومات التثبيت
        create_installer_info()
        
        print("\n🎉 تم إنشاء الحزمة النهائية بنجاح!")
        print("📂 تحقق من مجلد 'dist' للحصول على:")
        print("  📄 ملف exe مستقل")
        print("  📦 حزمة محمولة مضغوطة")
        print("  📋 ملف معلومات التثبيت")
        
        return True
    
    return False


if __name__ == "__main__":
    success = main()
    
    if success:
        print("\n✨ العملية مكتملة!")
    else:
        print("\n💥 فشلت العملية!")
    
    input("\n⏸️ اضغط Enter للخروج...")
