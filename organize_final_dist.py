#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تنظيم مجلد dist النهائي
Organize final dist folder
"""

import os
import shutil
import time
from pathlib import Path


def organize_dist_folder():
    """تنظيم مجلد dist وإنشاء هيكل نهائي منظم"""
    try:
        dist_dir = Path('dist')
        
        if not dist_dir.exists():
            print("❌ مجلد dist غير موجود")
            return False
        
        # إنشاء مجلد منظم جديد
        timestamp = time.strftime('%Y%m%d_%H%M%S')
        organized_dir = dist_dir / f"نظام_إدارة_المخازن_النهائي_{timestamp}"
        organized_dir.mkdir(exist_ok=True)
        
        print(f"📁 إنشاء مجلد منظم: {organized_dir.name}")
        
        # نسخ الملف التنفيذي المستقل
        exe_file = dist_dir / 'نظام_إدارة_المخازن_كامل.exe'
        if exe_file.exists():
            shutil.copy2(exe_file, organized_dir / 'نظام_إدارة_المخازن_مستقل.exe')
            print("✅ تم نسخ الملف التنفيذي المستقل")
        
        # نسخ الملف المضغوط
        zip_files = list(dist_dir.glob('نظام_إدارة_المخازن_محمول_*.zip'))
        if zip_files:
            latest_zip = max(zip_files, key=lambda x: x.stat().st_mtime)
            shutil.copy2(latest_zip, organized_dir / 'نظام_إدارة_المخازن_محمول.zip')
            print("✅ تم نسخ الملف المضغوط")
        
        # نسخ ملف معلومات التثبيت
        info_file = dist_dir / 'معلومات_التثبيت.txt'
        if info_file.exists():
            shutil.copy2(info_file, organized_dir)
            print("✅ تم نسخ ملف معلومات التثبيت")
        
        # إنشاء ملف README
        create_readme_file(organized_dir)
        
        # إنشاء ملف تشغيل سريع
        create_quick_start_file(organized_dir)
        
        # عرض ملخص النتائج
        show_final_summary(organized_dir)
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في تنظيم مجلد dist: {e}")
        return False


def create_readme_file(organized_dir):
    """إنشاء ملف README"""
    readme_content = """
# نظام إدارة المخازن والمستودعات
## الإصدار 2.0

### نظرة عامة
نظام شامل لإدارة المخازن والمستودعات مع واجهة عربية كاملة وميزات متقدمة.

### الملفات المتوفرة:

#### 1. نظام_إدارة_المخازن_مستقل.exe
- ملف تنفيذي واحد مستقل (48+ MB)
- يحتوي على جميع المكتبات المطلوبة
- لا يحتاج تثبيت أي مكونات إضافية
- انقر نقراً مزدوجاً لتشغيل البرنامج

#### 2. نظام_إدارة_المخازن_محمول.zip
- حزمة محمولة مضغوطة (47+ MB)
- تحتوي على البرنامج وجميع الملفات المساعدة
- استخرج الملف وشغل البرنامج من أي مكان

### بيانات تسجيل الدخول:
```
اسم المستخدم: admin
كلمة المرور: admin
```

### الميزات الرئيسية:
- ✅ إدارة شاملة للمخازن والمستودعات
- ✅ إدارة الأصناف والمواد مع تتبع الكميات
- ✅ إدارة المستفيدين والهيكل التنظيمي
- ✅ معاملات الصرف والاستلام
- ✅ تقارير مفصلة وإحصائيات
- ✅ نظام نسخ احتياطية تلقائي
- ✅ واجهة عربية كاملة مع دعم RTL
- ✅ استيراد وتصدير Excel
- ✅ طباعة التقارير والإيصالات

### المتطلبات التقنية:
- نظام التشغيل: Windows 10/11
- المعالج: Intel/AMD متوافق
- الذاكرة: 4 GB RAM (مُوصى به)
- مساحة القرص: 100 MB للتثبيت
- دقة الشاشة: 1024x768 أو أعلى

### التثبيت والتشغيل:
1. اختر أحد الخيارين المتوفرين
2. شغل البرنامج كمسؤول (اختياري)
3. استخدم بيانات تسجيل الدخول الافتراضية
4. ابدأ في إدخال البيانات

### الدعم والمساعدة:
- راجع ملف "معلومات_التثبيت.txt" للتفاصيل
- تحقق من مجلد logs في حالة وجود مشاكل
- احتفظ بنسخة احتياطية من مجلد data

### حقوق الطبع والنشر:
© 2025 - نظام إدارة المخازن والمستودعات
جميع الحقوق محفوظة
"""
    
    readme_file = organized_dir / 'README.txt'
    with open(readme_file, 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    print("✅ تم إنشاء ملف README")


def create_quick_start_file(organized_dir):
    """إنشاء ملف تشغيل سريع"""
    quick_start_content = """
# دليل التشغيل السريع

## الخطوة 1: اختيار طريقة التشغيل

### الطريقة الأولى (مُوصى بها):
1. انقر نقراً مزدوجاً على "نظام_إدارة_المخازن_مستقل.exe"
2. انتظر تحميل البرنامج (قد يستغرق 10-30 ثانية)
3. ستظهر شاشة تسجيل الدخول

### الطريقة الثانية:
1. استخرج ملف "نظام_إدارة_المخازن_محمول.zip"
2. انقر نقراً مزدوجاً على "نظام_إدارة_المخازن.exe"
3. ستظهر شاشة تسجيل الدخول

## الخطوة 2: تسجيل الدخول
- اسم المستخدم: admin
- كلمة المرور: admin
- انقر "تسجيل الدخول"

## الخطوة 3: البدء
1. ستظهر الشاشة الرئيسية
2. ابدأ بإعداد الهيكل التنظيمي (الوحدات والأقسام)
3. أضف الأصناف والمواد
4. أضف المستفيدين
5. ابدأ تسجيل المعاملات

## نصائح مهمة:
- احتفظ بنسخة احتياطية من البيانات دورياً
- استخدم ميزة التصدير لحفظ البيانات
- راجع التقارير للمتابعة والمراقبة

## في حالة وجود مشاكل:
1. تأكد من تشغيل البرنامج كمسؤول
2. تحقق من وجود مساحة كافية على القرص
3. راجع ملفات السجلات في مجلد logs
4. أعد تشغيل البرنامج

تاريخ الإنشاء: {date}
""".format(date=time.strftime('%Y-%m-%d %H:%M:%S'))
    
    quick_start_file = organized_dir / 'دليل_التشغيل_السريع.txt'
    with open(quick_start_file, 'w', encoding='utf-8') as f:
        f.write(quick_start_content)
    
    print("✅ تم إنشاء دليل التشغيل السريع")


def show_final_summary(organized_dir):
    """عرض ملخص النتائج النهائية"""
    print("\n" + "=" * 60)
    print("🎉 تم تنظيم مجلد dist بنجاح!")
    print("=" * 60)
    
    print(f"\n📁 المجلد المنظم: {organized_dir.name}")
    
    # عرض محتويات المجلد
    contents = list(organized_dir.iterdir())
    print(f"\n📋 المحتويات ({len(contents)} عنصر):")
    
    for item in sorted(contents):
        if item.is_file():
            size_mb = item.stat().st_size / (1024 * 1024)
            if size_mb > 1:
                print(f"  📄 {item.name} ({size_mb:.1f} MB)")
            else:
                size_kb = item.stat().st_size / 1024
                print(f"  📄 {item.name} ({size_kb:.1f} KB)")
        else:
            print(f"  📁 {item.name}/")
    
    print("\n" + "=" * 60)
    print("✨ البرنامج جاهز للتوزيع والاستخدام!")
    print("📦 يمكنك نسخ المجلد المنظم إلى أي مكان")
    print("🚀 ابدأ بتشغيل أحد الملفات التنفيذية")
    print("=" * 60)


def main():
    """الدالة الرئيسية"""
    print("🗂️ تنظيم مجلد dist النهائي")
    print("=" * 50)
    
    success = organize_dist_folder()
    
    if success:
        print("\n🎯 تم تنظيم المجلد بنجاح!")
    else:
        print("\n💥 فشل في تنظيم المجلد!")
    
    return success


if __name__ == "__main__":
    success = main()
    
    if success:
        print("\n✅ العملية مكتملة!")
    else:
        print("\n❌ فشلت العملية!")
    
    input("\n⏸️ اضغط Enter للخروج...")
