#!/usr/bin/env python3
"""
فحص قاعدة البيانات للتأكد من حفظ البيانات
"""

import sys
from pathlib import Path

# إضافة مسار المشروع
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from models import AddedItem
from database import db_manager

def check_database():
    """فحص البيانات المحفوظة في قاعدة البيانات"""
    
    print("🔍 فحص قاعدة البيانات...")
    
    try:
        # الحصول على جميع الأصناف
        items = AddedItem.get_all()
        print(f"📊 عدد الأصناف في قاعدة البيانات: {len(items)}")
        
        if len(items) == 0:
            print("⚠️ لا توجد أصناف في قاعدة البيانات")
            
            # فحص بنية الجدول
            print("\n🔧 فحص بنية جدول added_items:")
            columns = db_manager.fetch_all("PRAGMA table_info(added_items)")
            for col in columns:
                print(f"  {col[1]} ({col[2]}) - افتراضي: {col[4]}")
                
        else:
            print("\n📋 الأصناف المحفوظة:")
            for i, item in enumerate(items, 1):
                print(f"\n--- الصنف {i} ---")
                print(f"  ID: {item.id}")
                print(f"  رقم الصنف: {item.item_number}")
                print(f"  اسم الصنف: {item.item_name}")
                print(f"  الكمية الحالية: {item.current_quantity}")
                print(f"  الكمية المدخلة: {item.entered_quantity}")
                print(f"  مستخدم الإدخال: {item.data_entry_user}")
                print(f"  تاريخ الإدخال: {item.entry_date}")
                
    except Exception as e:
        print(f"❌ خطأ في فحص قاعدة البيانات: {e}")
        import traceback
        traceback.print_exc()

def test_manual_save():
    """اختبار حفظ يدوي لصنف واحد"""
    
    print("\n🧪 اختبار حفظ يدوي...")
    
    try:
        # إنشاء صنف تجريبي
        test_item = AddedItem(
            item_number="MANUAL001",
            item_name="اختبار يدوي",
            custody_type="مستهلكة",
            classification="اختبار",
            unit="عدد",
            current_quantity=999,
            entered_quantity=999,
            data_entry_user="اختبار يدوي",
            entry_date="2025-06-25",
            is_active=True
        )
        
        print(f"📦 الصنف قبل الحفظ:")
        print(f"  رقم الصنف: {test_item.item_number}")
        print(f"  الكمية الحالية: {test_item.current_quantity}")
        print(f"  الكمية المدخلة: {test_item.entered_quantity}")
        
        # محاولة الحفظ
        result = test_item.save()
        print(f"✅ نتيجة الحفظ: {result}")
        
        if result:
            print(f"📝 تم حفظ الصنف بـ ID: {test_item.id}")
            
            # التحقق من الحفظ
            saved_item = AddedItem.get_by_item_number("MANUAL001")
            if saved_item:
                print(f"✅ تم العثور على الصنف المحفوظ:")
                print(f"  الكمية الحالية: {saved_item.current_quantity}")
                print(f"  الكمية المدخلة: {saved_item.entered_quantity}")
            else:
                print("❌ لم يتم العثور على الصنف المحفوظ")
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار اليدوي: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    check_database()
    test_manual_save()
    print("\n" + "="*50)
    check_database()  # فحص مرة أخرى بعد الحفظ
