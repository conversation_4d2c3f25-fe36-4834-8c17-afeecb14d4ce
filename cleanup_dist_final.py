#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تنظيف مجلد dist والاحتفاظ بالمجلد المنظم النهائي فقط
Clean up dist folder and keep only the final organized folder
"""

import os
import shutil
from pathlib import Path


def cleanup_dist_folder():
    """تنظيف مجلد dist والاحتفاظ بالمجلد المنظم فقط"""
    try:
        dist_dir = Path('dist')
        
        if not dist_dir.exists():
            print("❌ مجلد dist غير موجود")
            return False
        
        # البحث عن المجلد المنظم النهائي
        final_dirs = [d for d in dist_dir.iterdir() 
                     if d.is_dir() and 'نظام_إدارة_المخازن_النهائي' in d.name]
        
        if not final_dirs:
            print("❌ لم يتم العثور على المجلد المنظم النهائي")
            return False
        
        # أخذ أحدث مجلد منظم
        final_dir = max(final_dirs, key=lambda x: x.stat().st_mtime)
        
        print(f"📁 المجلد المنظم النهائي: {final_dir.name}")
        print("🧹 بدء عملية التنظيف...")
        
        # قائمة العناصر في مجلد dist
        all_items = list(dist_dir.iterdir())
        items_to_remove = [item for item in all_items if item != final_dir]
        
        print(f"\n📋 العناصر المراد حذفها ({len(items_to_remove)} عنصر):")
        
        # حذف العناصر غير المطلوبة
        for item in items_to_remove:
            try:
                if item.is_file():
                    print(f"  🗑️ حذف ملف: {item.name}")
                    item.unlink()
                elif item.is_dir():
                    print(f"  🗑️ حذف مجلد: {item.name}")
                    shutil.rmtree(item)
            except Exception as e:
                print(f"  ❌ خطأ في حذف {item.name}: {e}")
        
        # إعادة تسمية المجلد النهائي
        new_name = "نظام_إدارة_المخازن_كامل"
        new_path = dist_dir / new_name
        
        if new_path.exists():
            shutil.rmtree(new_path)
        
        final_dir.rename(new_path)
        print(f"\n✅ تم إعادة تسمية المجلد إلى: {new_name}")
        
        # عرض النتائج النهائية
        show_final_results(new_path)
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في تنظيف مجلد dist: {e}")
        return False


def show_final_results(final_path):
    """عرض النتائج النهائية"""
    print("\n" + "=" * 60)
    print("🎉 تم تنظيف مجلد dist بنجاح!")
    print("=" * 60)
    
    print(f"\n📁 المجلد النهائي: {final_path.name}")
    
    # عرض محتويات المجلد النهائي
    contents = list(final_path.iterdir())
    print(f"\n📋 المحتويات النهائية ({len(contents)} عنصر):")
    
    total_size = 0
    for item in sorted(contents):
        if item.is_file():
            size_bytes = item.stat().st_size
            total_size += size_bytes
            size_mb = size_bytes / (1024 * 1024)
            
            if size_mb > 1:
                print(f"  📄 {item.name} ({size_mb:.1f} MB)")
            else:
                size_kb = size_bytes / 1024
                print(f"  📄 {item.name} ({size_kb:.1f} KB)")
        else:
            print(f"  📁 {item.name}/")
    
    total_size_mb = total_size / (1024 * 1024)
    print(f"\n📊 الحجم الإجمالي: {total_size_mb:.1f} MB")
    
    print("\n" + "=" * 60)
    print("✨ البرنامج جاهز للتوزيع!")
    print("📦 يمكنك نسخ مجلد 'نظام_إدارة_المخازن_كامل' إلى أي مكان")
    print("🚀 ابدأ بتشغيل أحد الملفات التنفيذية")
    print("=" * 60)
    
    # عرض تعليمات الاستخدام
    print("\n📋 تعليمات الاستخدام:")
    print("1️⃣ للتشغيل المباشر: انقر على 'نظام_إدارة_المخازن_مستقل.exe'")
    print("2️⃣ للنسخة المحمولة: استخرج 'نظام_إدارة_المخازن_محمول.zip'")
    print("3️⃣ بيانات الدخول: admin / admin")
    print("4️⃣ راجع ملفات README و دليل التشغيل للمزيد من التفاصيل")


def create_distribution_info():
    """إنشاء ملف معلومات التوزيع"""
    try:
        dist_dir = Path('dist')
        final_dir = dist_dir / "نظام_إدارة_المخازن_كامل"
        
        if not final_dir.exists():
            return False
        
        info_content = """
# نظام إدارة المخازن والمستودعات - معلومات التوزيع

## نظرة عامة
هذا المجلد يحتوي على النسخة النهائية الكاملة من نظام إدارة المخازن والمستودعات.

## محتويات المجلد:

### الملفات التنفيذية:
1. **نظام_إدارة_المخازن_مستقل.exe** (48+ MB)
   - ملف تنفيذي واحد مستقل
   - يحتوي على جميع المكتبات المطلوبة
   - لا يحتاج تثبيت أي مكونات إضافية

2. **نظام_إدارة_المخازن_محمول.zip** (47+ MB)
   - حزمة محمولة مضغوطة
   - تحتوي على البرنامج وجميع الملفات المساعدة
   - يمكن استخراجها وتشغيلها من أي مكان

### ملفات التوثيق:
- **README.txt**: دليل شامل للبرنامج
- **دليل_التشغيل_السريع.txt**: خطوات التشغيل السريع
- **معلومات_التثبيت.txt**: تفاصيل التثبيت والمتطلبات

## طريقة التوزيع:
1. انسخ هذا المجلد كاملاً إلى الجهاز المطلوب
2. اختر طريقة التشغيل المناسبة
3. ابدأ استخدام البرنامج

## الدعم الفني:
راجع ملفات التوثيق المرفقة للحصول على المساعدة والدعم.

---
تم إنشاء هذا التوزيع في: {date}
إصدار البرنامج: 2.0
""".format(date=time.strftime('%Y-%m-%d %H:%M:%S'))
        
        info_file = final_dir / 'معلومات_التوزيع.txt'
        with open(info_file, 'w', encoding='utf-8') as f:
            f.write(info_content)
        
        print("✅ تم إنشاء ملف معلومات التوزيع")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء ملف معلومات التوزيع: {e}")
        return False


def main():
    """الدالة الرئيسية"""
    print("🧹 تنظيف مجلد dist النهائي")
    print("=" * 50)
    
    success = cleanup_dist_folder()
    
    if success:
        # إنشاء ملف معلومات التوزيع
        create_distribution_info()
        
        print("\n🎯 تم تنظيف المجلد بنجاح!")
        print("📁 مجلد dist يحتوي الآن على المجلد المنظم فقط")
    else:
        print("\n💥 فشل في تنظيف المجلد!")
    
    return success


if __name__ == "__main__":
    import time
    
    success = main()
    
    if success:
        print("\n✅ العملية مكتملة!")
        print("🎉 البرنامج جاهز للتوزيع والاستخدام!")
    else:
        print("\n❌ فشلت العملية!")
    
    input("\n⏸️ اضغط Enter للخروج...")
