#!/usr/bin/env python3
"""
اختبار عرض البيانات في شاشة إدارة الأصناف
"""

import sys
from pathlib import Path
from datetime import datetime

# إضافة مسار المشروع
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from models import AddedItem

def test_ui_display():
    """اختبار عرض البيانات في الواجهة"""
    
    print("🔍 اختبار عرض البيانات في شاشة إدارة الأصناف...")
    
    # إنشاء صنف تجريبي جديد
    test_item = AddedItem(
        item_number="UI_TEST_001",
        item_name="اختبار واجهة المستخدم",
        custody_type="مستهلكة",
        classification="اختبار",
        unit="عدد",
        current_quantity=777,
        entered_quantity=777,
        data_entry_user="اختبار الواجهة",
        entry_date=datetime.now().strftime('%Y-%m-%d'),
        is_active=True
    )
    
    print("📦 إنشاء صنف تجريبي...")
    print(f"  رقم الصنف: {test_item.item_number}")
    print(f"  اسم الصنف: {test_item.item_name}")
    print(f"  الكمية الحالية: {test_item.current_quantity}")
    print(f"  الكمية المدخلة: {test_item.entered_quantity}")
    
    # حفظ الصنف
    if test_item.save():
        print(f"✅ تم حفظ الصنف بـ ID: {test_item.id}")
        
        # التحقق من الحفظ
        saved_item = AddedItem.get_by_item_number("UI_TEST_001")
        if saved_item:
            print(f"✅ تم التحقق من الحفظ:")
            print(f"  الكمية الحالية: {saved_item.current_quantity}")
            print(f"  الكمية المدخلة: {saved_item.entered_quantity}")
        else:
            print("❌ فشل في التحقق من الحفظ")
    else:
        print("❌ فشل في حفظ الصنف")
    
    # عرض جميع الأصناف
    print(f"\n📋 جميع الأصناف في قاعدة البيانات:")
    all_items = AddedItem.get_all()
    
    for item in all_items:
        if item.item_number.startswith(('UI_TEST', 'FINAL', 'MANUAL')):
            print(f"  {item.item_number} | {item.item_name}")
            print(f"    الكمية الحالية: {item.current_quantity}")
            print(f"    الكمية المدخلة: {item.entered_quantity}")
            print(f"    مستخدم الإدخال: {item.data_entry_user}")
            print()

if __name__ == "__main__":
    test_ui_display()
