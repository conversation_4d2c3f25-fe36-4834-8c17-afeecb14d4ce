#!/usr/bin/env python3
"""
اختبار نهائي لاستيراد Excel مع الكميات الصحيحة
"""

import pandas as pd
import sys
from pathlib import Path
from datetime import datetime

# إضافة مسار المشروع
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from models import AddedItem

def test_excel_import_final():
    """اختبار نهائي لاستيراد Excel"""
    
    print("🔍 اختبار نهائي لاستيراد Excel...")
    
    # إنشاء بيانات تجريبية جديدة
    test_data = {
        'الرقم': [1, 2, 3],
        'رقم الصنف': ['FINAL001', 'FINAL002', 'FINAL003'],
        'اسم الصنف': ['اختبار نهائي 1', 'اختبار نهائي 2', 'اختبار نهائي 3'],
        'نوع العهدة': ['مستهلكة', 'مستديمة', 'مستهلكة'],
        'التصنيف': ['اختبار', 'اختبار', 'اختبار'],
        'الوحدة': ['عدد', 'عدد', 'عدد'],
        'الكمية الحالية': [500, 300, 150],
        'الكمية المدخلة': [500, 300, 150]
    }
    
    # إنشاء ملف Excel
    df = pd.DataFrame(test_data)
    file_path = 'test_final_import.xlsx'
    df.to_excel(file_path, index=False, engine='openpyxl')
    print(f"✅ تم إنشاء ملف الاختبار: {file_path}")
    
    # محاكاة عملية الاستيراد
    print("\n📊 محاكاة عملية الاستيراد...")
    
    success_count = 0
    error_count = 0
    
    for index, row in df.iterrows():
        try:
            print(f"\n--- معالجة الصف {index + 1} ---")
            
            # استخراج البيانات
            current_qty = int(row.get('الكمية الحالية', 0)) if not pd.isna(row.get('الكمية الحالية', 0)) else 0
            entered_qty = int(row.get('الكمية المدخلة', 0)) if not pd.isna(row.get('الكمية المدخلة', 0)) else 0
            
            print(f"  رقم الصنف: {row['رقم الصنف']}")
            print(f"  اسم الصنف: {row['اسم الصنف']}")
            print(f"  الكمية الحالية: {current_qty}")
            print(f"  الكمية المدخلة: {entered_qty}")
            
            # إنشاء الصنف
            new_item = AddedItem(
                item_number=str(row['رقم الصنف']),
                item_name=str(row['اسم الصنف']),
                custody_type=str(row.get('نوع العهدة', '')),
                classification=str(row.get('التصنيف', '')),
                unit=str(row.get('الوحدة', 'عدد')),
                current_quantity=current_qty,
                entered_quantity=entered_qty,
                data_entry_user="اختبار نهائي",
                entry_date=datetime.now().strftime('%Y-%m-%d'),
                is_active=True
            )
            
            # حفظ الصنف
            if new_item.save():
                print(f"  ✅ تم حفظ الصنف بـ ID: {new_item.id}")
                success_count += 1
                
                # التحقق من الحفظ
                saved_item = AddedItem.get_by_item_number(str(row['رقم الصنف']))
                if saved_item:
                    print(f"  ✅ تم التحقق: الكمية الحالية={saved_item.current_quantity}, الكمية المدخلة={saved_item.entered_quantity}")
                else:
                    print(f"  ❌ فشل في التحقق من الحفظ")
            else:
                print(f"  ❌ فشل في حفظ الصنف")
                error_count += 1
                
        except Exception as e:
            print(f"  ❌ خطأ في معالجة الصف: {e}")
            error_count += 1
    
    print(f"\n📊 نتائج الاستيراد:")
    print(f"  ✅ نجح: {success_count}")
    print(f"  ❌ فشل: {error_count}")
    
    # عرض جميع الأصناف المحفوظة
    print(f"\n📋 جميع الأصناف في قاعدة البيانات:")
    all_items = AddedItem.get_all()
    for item in all_items:
        if item.item_number.startswith('FINAL'):
            print(f"  {item.item_number} | {item.item_name} | حالية: {item.current_quantity} | مدخلة: {item.entered_quantity}")

if __name__ == "__main__":
    test_excel_import_final()
