#!/usr/bin/env python3
"""
مدير الطباعة المحسن - دعم كامل للعربية وتصميم احترافي
Enhanced Print Manager - Full Arabic Support & Professional Design
"""

import os
import tempfile
import webbrowser
from datetime import datetime
from pathlib import Path
import sys

# إضافة مسار المشروع
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

class PrintManager:
    """مدير الطباعة المحسن"""
    
    def __init__(self):
        self.temp_files = []
    
    def _get_hijri_date(self, gregorian_date=None):
        """الحصول على التاريخ الهجري التقريبي"""
        try:
            # تحويل تقريبي للتاريخ الهجري
            from datetime import datetime
            if gregorian_date is None:
                gregorian_date = datetime.now()
            
            # تحويل تقريبي (يمكن تحسينه باستخدام مكتبة hijri-converter)
            hijri_year = gregorian_date.year - 579
            hijri_month = gregorian_date.month
            hijri_day = gregorian_date.day
            
            # أسماء الأشهر الهجرية
            hijri_months = [
                "محرم", "صفر", "ربيع الأول", "ربيع الثاني", "جمادى الأولى", "جمادى الثانية",
                "رجب", "شعبان", "رمضان", "شوال", "ذو القعدة", "ذو الحجة"
            ]
            
            month_name = hijri_months[hijri_month - 1] if hijri_month <= 12 else "محرم"
            
            return f"{hijri_day} {month_name} {hijri_year}هـ"
        except:
            return "_____ / _____ / _____هـ"
    
    def create_transaction_html(self, transaction, beneficiary, receiver, transaction_items,
                              rank="", department="", receiver_rank="", receiver_department="", notes="", auto_print=False, current_user="admin"):
        """إنشاء HTML محسن لسند الصرف وفقاً للمتطلبات الرسمية"""
        

        
        # تحضير البيانات
        # استخدام تاريخ عمل السند وليس تاريخ الطباعة
        if hasattr(transaction, 'transaction_date') and transaction.transaction_date:
            if isinstance(transaction.transaction_date, str):
                transaction_date = transaction.transaction_date
                try:
                    date_obj = datetime.strptime(transaction.transaction_date, '%Y-%m-%d')
                except:
                    date_obj = datetime.now()
            else:
                transaction_date = transaction.transaction_date.strftime('%Y-%m-%d')
                date_obj = transaction.transaction_date if isinstance(transaction.transaction_date, datetime) else datetime.combine(transaction.transaction_date, datetime.min.time())
        elif hasattr(transaction, 'created_at') and transaction.created_at:
            transaction_date = transaction.created_at.strftime('%Y-%m-%d')
            date_obj = transaction.created_at
        else:
            transaction_date = datetime.now().strftime('%Y-%m-%d')
            date_obj = datetime.now()
            
        hijri_date = self._get_hijri_date(date_obj)
        print_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        # حساب إجمالي الكميات
        total_quantity = sum(int(float(item.get('quantity', 0))) for item in transaction_items)
        
        html_content = f"""
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>سند صرف رقم {transaction.transaction_number}</title>
    <style>
        /* ✅ دعم كامل للعربية - UTF-8 Encoding */
        @charset "UTF-8";
        
        /* ✅ خطوط عربية مدمجة - Arial, Tahoma, Segoe UI */
        * {{
            font-family: 'Segoe UI', 'Tahoma', 'Arial Unicode MS', 'Traditional Arabic', 'Arial', sans-serif;
            direction: rtl;
            text-align: right;
            box-sizing: border-box;
        }}
        
        body {{
            margin: 0;
            padding: 15px;
            background-color: #f8f9fa;
            color: #212529;
            line-height: 1.4;
            font-size: 12px;
            font-weight: 400;
        }}
        
        /* ✅ تصميم احترافي للطباعة - مُحسن لصفحة A4 واحدة */
        @media print {{
            body {{
                background-color: white;
                margin: 0;
                padding: 5px;
                font-size: 10px;
                line-height: 1.2;
            }}
            
            .no-print {{
                display: none !important;
            }}
            
            .print-container {{
                box-shadow: none !important;
                border: 1px solid #000 !important;
                border-radius: 0 !important;
                max-width: none !important;
                width: 100% !important;
                height: auto !important;
                max-height: none !important;
            }}
            
            .official-header {{
                border-bottom: 1px solid #000 !important;
                padding: 5px !important;
            }}
            
            .official-header h3 {{
                font-size: 10px !important;
                margin: 1px 0 !important;
            }}
            
            .document-info p {{
                font-size: 9px !important;
                margin: 1px 0 !important;
            }}
            
            .main-title {{
                font-size: 18px !important;
                font-weight: bold !important;
                margin: 5px 0 !important;
                padding: 5px !important;
            }}
            
            .content {{
                padding: 8px !important;
            }}
            
            .info-section {{
                margin-bottom: 8px !important;
            }}
            
            .section-title {{
                font-size: 12px !important;
                font-weight: bold !important;
                padding: 5px 8px !important;
            }}
            
            .section-content {{
                padding: 8px !important;
            }}
            
            .info-item {{
                margin-bottom: 4px !important;
            }}
            
            .info-label {{
                font-size: 10px !important;
                min-width: 80px !important;
            }}
            
            .info-value {{
                font-size: 10px !important;
            }}
            
            table {{
                font-size: 9px !important;
                margin: 8px 0 !important;
            }}
            
            .items-table th {{
                padding: 4px 3px !important;
                font-size: 9px !important;
            }}
            
            .items-table td {{
                padding: 3px 3px !important;
                font-size: 9px !important;
            }}
            
            .footer {{
                padding: 5px !important;
                font-size: 8px !important;
            }}
            
            .signatures-section {{
                display: flex !important;
                justify-content: space-between !important;
                gap: 10px !important;
                margin-top: 10px !important;
                page-break-inside: avoid !important;
            }}
            
            .beneficiary-section, .receiver-section {{
                flex: 1 !important;
                border: 1px solid #000 !important;
                margin: 0 !important;
            }}
            
            .signature-title {{
                font-size: 10px !important;
                padding: 3px 5px !important;
                text-align: center !important;
            }}
            
            .signature-content {{
                padding: 5px !important;
            }}
            
            .info-item-inline {{
                margin-bottom: 2px !important;
                font-size: 8px !important;
            }}
            
            .info-label-small {{
                min-width: 50px !important;
                font-size: 8px !important;
            }}
            
            .info-value-small {{
                font-size: 8px !important;
            }}
            
            .signature-box-inline {{
                padding: 2px !important;
                font-size: 8px !important;
                margin-top: 5px !important;
            }}
        }}
        
        /* ✅ حاوي رئيسي - صفحة A4 واحدة */
        .print-container {{
            max-width: 210mm;
            margin: 0 auto;
            background: white;
            border: 2px solid #000;
            height: 297mm;
            max-height: 297mm;
            padding: 0;
            overflow: hidden;
            page-break-after: avoid;
        }}
        
        /* ✅ الشريط العلوي الرسمي - مضغوط */
        .official-header {{
            padding: 8px;
            border-bottom: 2px solid #000;
            background: linear-gradient(135deg, #e9ecef 0%, #f8f9fa 100%);
        }}
        
        .header-row {{
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 5px;
        }}
        
        .official-info {{
            text-align: center;
            flex: 1;
        }}
        
        .official-info h3 {{
            margin: 1px 0;
            font-size: 12px;
            font-weight: bold;
        }}
        
        .document-info {{
            text-align: left;
            min-width: 180px;
        }}
        
        .document-info p {{
            margin: 1px 0;
            font-size: 10px;
        }}
        
        /* ✅ العنوان الرئيسي - مضغوط */
        .main-title {{
            text-align: center;
            font-size: 18px;
            font-weight: bold;
            margin: 8px 0;
            padding: 8px;
            border: 2px solid #000;
            background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
            color: white;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
        }}
        
        /* ✅ محتوى السند - مضغوط لصفحة واحدة */
        .content {{
            padding: 10px;
            height: auto;
            overflow: visible;
        }}
        
        /* ✅ أقسام المعلومات - مضغوطة لصفحة واحدة */
        .info-section {{
            margin-bottom: 8px;
            border: 1px solid #dee2e6;
            border-radius: 3px;
            overflow: hidden;
            box-shadow: 0 1px 2px rgba(0,0,0,0.1);
        }}
        
        .section-title {{
            font-size: 14px;
            font-weight: bold;
            margin: 0;
            padding: 6px 10px;
            background: linear-gradient(135deg, #e9ecef 0%, #f8f9fa 100%);
            border-bottom: 1px solid #dee2e6;
            color: #495057;
        }}
        
        .section-content {{
            padding: 10px;
            background: white;
        }}
        
        .info-grid {{
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 6px;
        }}
        
        .info-item {{
            display: flex;
            margin-bottom: 4px;
        }}
        
        .info-label {{
            font-weight: bold;
            min-width: 100px;
            margin-left: 8px;
            font-size: 11px;
        }}
        
        .info-value {{
            border-bottom: 1px solid #000;
            flex: 1;
            padding-bottom: 1px;
            font-size: 11px;
        }}
        
        /* ✅ جدول الأصناف الرسمي - مضغوط */
        .items-table {{
            width: 100%;
            border-collapse: collapse;
            margin: 8px 0;
            border: 1px solid #495057;
            border-radius: 3px;
            overflow: hidden;
        }}
        
        .items-table th {{
            background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
            color: white;
            padding: 6px 4px;
            font-weight: bold;
            text-align: center;
            border: 1px solid #495057;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
            font-size: 11px;
        }}
        
        .items-table td {{
            padding: 4px 4px;
            border: 1px solid #dee2e6;
            text-align: center;
            font-size: 10px;
        }}
        
        /* ✅ صفوف متناوبة - ألوان مختلفة للوضوح */
        .items-table tbody tr:nth-child(even) {{
            background-color: #f8f9fa;
        }}
        
        .items-table tbody tr:nth-child(odd) {{
            background-color: white;
        }}
        
        .items-table tbody tr:hover {{
            background-color: #e3f2fd;
        }}
        
        /* ✅ تمييز لوني - أخضر للمتوفر، أحمر لغير المتوفر */
        .status-available {{
            background-color: #d4edda !important;
            color: #155724;
            font-weight: bold;
        }}
        
        .status-unavailable {{
            background-color: #f8d7da !important;
            color: #721c24;
            font-weight: bold;
        }}
        
        .quantity-high {{
            background-color: #d1ecf1;
            color: #0c5460;
        }}
        
        .quantity-low {{
            background-color: #fff3cd;
            color: #856404;
        }}
        
        .quantity-zero {{
            background-color: #f8d7da;
            color: #721c24;
        }}
        
        /* ✅ الإقرار - تصميم احترافي */
        .acknowledgment-section {{
            margin: 30px 0;
            padding: 20px;
            border: 2px solid #495057;
            border-radius: 5px;
            text-align: center;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }}
        
        .acknowledgment-text {{
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 20px;
            color: #495057;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
        }}
        
        /* ✅ قسم التوقيعات - تصميم احترافي */
        .signature-section {{
            margin-top: 40px;
            display: flex;
            justify-content: space-between;
            padding: 20px 0;
            gap: 20px;
        }}
        
        .signature-box {{
            width: 45%;
            text-align: center;
            border: 2px solid #495057;
            border-radius: 5px;
            padding: 20px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }}
        
        .signature-title {{
            font-weight: bold;
            margin-bottom: 30px;
            padding: 10px;
            background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
            color: white;
            border-radius: 3px;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
        }}
        
        .signature-line {{
            border-bottom: 2px solid #495057;
            height: 40px;
            margin: 15px 0;
            background: white;
        }}
        
        .date-line {{
            border-bottom: 1px solid #6c757d;
            height: 30px;
            margin: 10px 0;
            background: white;
        }}
        
        /* ✅ قسم التوقيعات - بيانات المستفيد والمندوب في خط واحد */
        .signatures-section {{
            display: flex;
            justify-content: space-between;
            gap: 20px;
            margin-top: 15px;
            page-break-inside: avoid;
        }}
        
        .beneficiary-section, .receiver-section {{
            flex: 1;
            border: 1px solid #dee2e6;
            border-radius: 3px;
            overflow: hidden;
            box-shadow: 0 1px 2px rgba(0,0,0,0.1);
        }}
        
        .signature-title {{
            font-size: 12px;
            font-weight: bold;
            margin: 0;
            padding: 6px 10px;
            background: linear-gradient(135deg, #e9ecef 0%, #f8f9fa 100%);
            border-bottom: 1px solid #dee2e6;
            color: #495057;
            text-align: center;
        }}
        
        .signature-content {{
            padding: 8px;
            background: white;
        }}
        
        .signature-info {{
            margin-bottom: 8px;
        }}
        
        .info-item-inline {{
            display: flex;
            margin-bottom: 3px;
            font-size: 10px;
        }}
        
        .info-label-small {{
            font-weight: bold;
            min-width: 70px;
            margin-left: 5px;
            font-size: 9px;
        }}
        
        .info-value-small {{
            border-bottom: 1px solid #000;
            flex: 1;
            padding-bottom: 1px;
            font-size: 9px;
        }}
        
        .signature-box-inline {{
            text-align: center;
            margin-top: 8px;
            padding: 4px;
            border: 1px solid #000;
            font-size: 10px;
        }}
        
        /* ✅ تذييل السند - مضغوط */
        .footer {{
            background: white;
            padding: 8px;
            text-align: center;
            border-top: 1px solid #bdc3c7;
            color: #000000;
            font-size: 9px;
            font-weight: bold;
        }}
        
        /* ✅ أزرار الطباعة */
        .print-buttons {{
            text-align: center;
            padding: 20px;
            background: #ecf0f1;
        }}
        
        .print-btn {{
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 25px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            margin: 0 10px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }}
        
        .print-btn:hover {{
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(0,0,0,0.2);
        }}
        
        .close-btn {{
            background: linear-gradient(135deg, #95a5a6, #7f8c8d);
        }}
        
        /* ✅ تحسينات إضافية */
        .highlight {{
            background: #fff3cd;
            padding: 2px 6px;
            border-radius: 3px;
            font-weight: bold;
        }}
        
        .badge {{
            background: #3498db;
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
        }}
        
        /* ✅ تخطيط مرن للشاشات الصغيرة */
        @media (max-width: 768px) {{
            .print-container {{
                margin: 10px;
                border-radius: 5px;
            }}
            
            .content {{
                padding: 20px;
            }}
            
            .info-grid {{
                grid-template-columns: 1fr;
            }}
            
            .items-table {{
                font-size: 12px;
            }}
            
            .items-table th,
            .items-table td {{
                padding: 8px;
            }}
        }}
    </style>
</head>
<body>
    <div class="print-container">
        <!-- ✅ الشريط العلوي الرسمي -->
        <div class="official-header">
            <div class="header-row">
                <div class="official-info">
                    <h3>المملكة العربية السعودية</h3>
                    <h3>وزارة الدفاع</h3>
                    <h3>رئاسة هيئة الأركان العامة</h3>
                    <h3>قوات الدفاع الجوي الملكي السعودي</h3>
                </div>
                <div class="document-info">
                    <p><strong>رقم السند:</strong> {transaction.transaction_number}</p>
                    <p><strong>التاريخ الميلادي:</strong> {transaction_date}</p>
                    <p><strong>التاريخ الهجري:</strong> {hijri_date}</p>
                    <p><strong>الموضوع:</strong> سند صرف</p>
                </div>
            </div>
        </div>
        
        <!-- ✅ العنوان الرئيسي - بخط كبير وبارز -->
        <div class="main-title">
            سند صرف
        </div>
        
        <!-- ✅ محتوى السند -->
        <div class="content">
            <!-- 1. الأصناف المصروفة -->
            <div class="info-section">
                <h3 class="section-title">1. الأصناف المصروفة ({len(transaction_items)} أصناف)</h3>
                <div class="section-content">
                    <table class="items-table">
                    <thead>
                        <tr>
                            <th style="width: 30px;">#</th>
                            <th>اسم الصنف</th>
                            <th style="width: 50px;">الكمية</th>
                            <th style="width: 100px;">ملاحظات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {self._generate_items_rows_official(transaction_items)}
                    </tbody>
                    </table>
                </div>
            </div>
            
            <!-- 2. بيانات المستفيد والمندوب المستلم في خط واحد أسفل السند -->
            <div class="signatures-section">
                <!-- بيانات المستفيد - يمين -->
                <div class="beneficiary-section">
                    <h3 class="signature-title">بيانات المستفيد</h3>
                    <div class="signature-content">
                        <div class="signature-info">
                            <div class="info-item-inline">
                                <span class="info-label-small">الاسم:</span>
                                <span class="info-value-small">{beneficiary.name if beneficiary else '___________________'}</span>
                            </div>
                            <div class="info-item-inline">
                                <span class="info-label-small">الرقم العسكري:</span>
                                <span class="info-value-small">{beneficiary.number if beneficiary and beneficiary.number else '___________________'}</span>
                            </div>
                            <div class="info-item-inline">
                                <span class="info-label-small">الرتبة:</span>
                                <span class="info-value-small">{rank or '___________________'}</span>
                            </div>
                            <div class="info-item-inline">
                                <span class="info-label-small">الإدارة:</span>
                                <span class="info-value-small">{department or '___________________'}</span>
                            </div>
                            <div class="info-item-inline">
                                <span class="info-label-small">القسم:</span>
                                <span class="info-value-small">{department or '___________________'}</span>
                            </div>
                        </div>
                        <div class="signature-box-inline">
                            <strong>التوقيع: ___________________</strong>
                        </div>
                    </div>
                </div>
                
                <!-- بيانات المندوب المستلم - يسار -->
                <div class="receiver-section">
                    <h3 class="signature-title">بيانات المندوب المستلم</h3>
                    <div class="signature-content">
                        <div class="signature-info">
                            <div class="info-item-inline">
                                <span class="info-label-small">الاسم:</span>
                                <span class="info-value-small">{receiver.name if receiver and hasattr(receiver, 'name') and receiver.name else '___________________'}</span>
                            </div>
                            <div class="info-item-inline">
                                <span class="info-label-small">الرقم العسكري:</span>
                                <span class="info-value-small">{receiver.number if receiver and hasattr(receiver, 'number') and receiver.number else '___________________'}</span>
                            </div>
                            <div class="info-item-inline">
                                <span class="info-label-small">الرتبة:</span>
                                <span class="info-value-small">{receiver_rank if receiver_rank and str(receiver_rank).strip() and str(receiver_rank).strip() != '' else '___________________'}</span>
                            </div>
                            <div class="info-item-inline">
                                <span class="info-label-small">الإدارة:</span>
                                <span class="info-value-small">{receiver_department if receiver_department and str(receiver_department).strip() and str(receiver_department).strip() != '' else '___________________'}</span>
                            </div>
                            <div class="info-item-inline">
                                <span class="info-label-small">القسم:</span>
                                <span class="info-value-small">{receiver_department if receiver_department and str(receiver_department).strip() and str(receiver_department).strip() != '' else '___________________'}</span>
                            </div>
                        </div>
                        <div class="signature-box-inline">
                            <strong>التوقيع: ___________________</strong>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- ✅ تذييل السند -->
        <div class="footer">
            <p>تاريخ الطباعة: {print_time} | مستخدم النظام: {current_user}</p>
        </div>
        
        <!-- ✅ أزرار الطباعة -->
        <div class="print-buttons no-print">
            <button class="print-btn" onclick="printDocument()">🖨️ طباعة</button>
            <button class="print-btn close-btn" onclick="closeWindow()">❌ إغلاق</button>
        </div>
    </div>
    
    <script>
        // ✅ طباعة تلقائية عند فتح الصفحة (حسب الإعداد)
        window.onload = function() {{
            // تأخير قصير للسماح بتحميل الصفحة كاملة
            setTimeout(function() {{
                {'window.print();' if auto_print else '// الطباعة التلقائية معطلة - يمكن الطباعة يدوياً'}
            }}, 500);
        }};
        
        // ✅ دالة الطباعة اليدوية
        function printDocument() {{
            window.print();
        }}
        
        // ✅ دالة إغلاق النافذة (يدوية فقط)
        function closeWindow() {{
            if (confirm('هل تريد إغلاق النافذة؟')) {{
                window.close();
            }}
        }}
        
        // ✅ رسالة ترحيبية
        console.log('سند الصرف جاهز للمعاينة والطباعة');
    </script>
</body>
</html>
        """
        
        return html_content
    
    def _generate_receiver_section(self, receiver, receiver_rank, receiver_department):
        """إنشاء قسم معلومات المندوب المستلم"""
        if not receiver:
            return ""
        
        return f"""
            <!-- معلومات المندوب المستلم -->
            <div class="info-section">
                <h2 class="section-title">
                    <span class="section-icon">🤝</span>
                    معلومات المندوب المستلم
                </h2>
                <div class="info-grid">
                    <div class="info-item">
                        <span class="info-label">الاسم:</span>
                        <span class="info-value">{receiver.name}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">الرقم العام:</span>
                        <span class="info-value">{receiver.number if receiver.number else 'غير محدد'}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">الرتبة:</span>
                        <span class="info-value">{receiver_rank or 'غير محدد'}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">الإدارة/القسم:</span>
                        <span class="info-value">{receiver_department or 'غير محدد'}</span>
                    </div>
                </div>
            </div>
        """
    
    def _generate_receiver_section_official_new(self, receiver, receiver_rank, receiver_department):
        """إنشاء قسم معلومات المندوب المستلم بالتصميم الجديد"""
        if not receiver:
            return """
            <!-- 3. بيانات المندوب المستلم -->
            <div class="info-section">
                <h3 class="section-title">3. بيانات المندوب المستلم</h3>
                <div class="section-content">
                    <div class="info-grid">
                        <div class="info-item">
                            <span class="info-label">الاسم:</span>
                            <span class="info-value">___________________</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">الرقم العسكري:</span>
                            <span class="info-value">___________________</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">الرتبة:</span>
                            <span class="info-value">___________________</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">الإدارة:</span>
                            <span class="info-value">___________________</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">القسم:</span>
                            <span class="info-value">___________________</span>
                        </div>
                    </div>
                    <div style="text-align: center; margin-top: 10px; padding: 5px; border: 1px solid #000; font-size: 11px;">
                        <strong>التوقيع: ___________________</strong>
                    </div>
                </div>
            </div>
            """
        
        return f"""
            <!-- 3. بيانات المندوب المستلم -->
            <div class="info-section">
                <h3 class="section-title">3. بيانات المندوب المستلم</h3>
                <div class="section-content">
                    <div class="info-grid">
                        <div class="info-item">
                            <span class="info-label">الاسم:</span>
                            <span class="info-value">{receiver.name}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">الرقم العسكري:</span>
                            <span class="info-value">{receiver.number if receiver.number else '___________________'}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">الرتبة:</span>
                            <span class="info-value">{receiver_rank or '___________________'}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">الإدارة:</span>
                            <span class="info-value">{receiver_department or '___________________'}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">القسم:</span>
                            <span class="info-value">{receiver_department or '___________________'}</span>
                        </div>
                    </div>
                    <div style="text-align: center; margin-top: 10px; padding: 5px; border: 1px solid #000; font-size: 11px;">
                        <strong>التوقيع: ___________________</strong>
                    </div>
                </div>
            </div>
        """

    def _generate_receiver_section_official(self, receiver, receiver_rank, receiver_department):
        """إنشاء قسم معلومات المندوب المستلم بالتصميم الرسمي"""
        if not receiver:
            return """
            <!-- بيانات المندوب المستلم -->
            <div class="info-section">
                <h3 class="section-title">بيانات المندوب المستلم</h3>
                <div class="section-content">
                    <div class="info-grid">
                        <div class="info-item">
                            <span class="info-label">الاسم:</span>
                            <span class="info-value">___________________</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">الرقم العسكري:</span>
                            <span class="info-value">___________________</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">الرتبة:</span>
                            <span class="info-value">___________________</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">الإدارة:</span>
                            <span class="info-value">___________________</span>
                        </div>
                    </div>
                    <div class="info-item">
                        <span class="info-label">القسم:</span>
                        <span class="info-value">___________________</span>
                    </div>
                </div>
            </div>
            """
        
        return f"""
            <!-- بيانات المندوب المستلم -->
            <div class="info-section">
                <h3 class="section-title">بيانات المندوب المستلم</h3>
                <div class="section-content">
                    <div class="info-grid">
                        <div class="info-item">
                            <span class="info-label">الاسم:</span>
                            <span class="info-value">{receiver.name}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">الرقم العسكري:</span>
                            <span class="info-value">{receiver.number if receiver.number else '___________________'}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">الرتبة:</span>
                            <span class="info-value">{receiver_rank or '___________________'}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">الإدارة:</span>
                            <span class="info-value">{receiver_department or '___________________'}</span>
                        </div>
                    </div>
                    <div class="info-item">
                        <span class="info-label">القسم:</span>
                        <span class="info-value">{receiver_department or '___________________'}</span>
                    </div>
                </div>
            </div>
        """
    
    def _generate_items_rows_official(self, transaction_items):
        """إنشاء صفوف جدول الأصناف بالتصميم الرسمي"""
        rows = []
        for i, item in enumerate(transaction_items, 1):
            quantity = int(float(item.get('quantity', 0)))
            item_name = item.get('item_name', 'غير محدد')
            notes = item.get('notes', '')
            
            row = f"""
                <tr>
                    <td>{i}</td>
                    <td style="text-align: right;">{item_name}</td>
                    <td>{quantity}</td>
                    <td style="text-align: right;">{notes}</td>
                </tr>
            """
            rows.append(row)
        
        return "".join(rows)
    
    def _generate_items_rows(self, transaction_items):
        """إنشاء صفوف جدول الأصناف"""
        rows = []
        for i, item in enumerate(transaction_items, 1):
            quantity = int(float(item.get('quantity', 0)))
            item_name = item.get('item_name', 'غير محدد')
            notes = item.get('notes', 'لا توجد ملاحظات')
            
            # تحديد لون الكمية حسب التوفر
            quantity_class = "quantity-available" if quantity > 0 else "quantity-unavailable"
            
            row = f"""
                <tr>
                    <td><strong>{i}</strong></td>
                    <td style="text-align: right;">{item_name}</td>
                    <td><span class="{quantity_class}">{quantity}</span></td>
                    <td style="text-align: right;">{notes}</td>
                </tr>
            """
            rows.append(row)
        
        return "".join(rows)
    
    def _generate_notes_section(self, notes):
        """إنشاء قسم الملاحظات"""
        if not notes or notes.strip() == "":
            return ""
        
        return f"""
            <!-- الملاحظات -->
            <div class="notes-section">
                <div class="notes-title">📝 ملاحظات:</div>
                <div class="notes-content">{notes}</div>
            </div>
        """
    
    def print_transaction(self, transaction, beneficiary, receiver, transaction_items,
                         rank="", department="", receiver_rank="", receiver_department="", notes="", auto_print=False, current_user="admin"):
        """طباعة سند الصرف بالتصميم المحسن"""
        try:
            # إنشاء محتوى HTML
            html_content = self.create_transaction_html(
                transaction, beneficiary, receiver, transaction_items,
                rank, department, receiver_rank, receiver_department, notes, auto_print, current_user
            )
            
            # إنشاء ملف HTML مؤقت
            with tempfile.NamedTemporaryFile(mode='w', suffix='.html', delete=False, encoding='utf-8') as temp_file:
                temp_file.write(html_content)
                temp_file_path = temp_file.name
            
            # إضافة الملف للقائمة للحذف لاحقاً
            self.temp_files.append(temp_file_path)
            
            # فتح الملف في المتصفح للطباعة
            webbrowser.open(f'file://{temp_file_path}')
            
            return True
            
        except Exception as e:
            print(f"خطأ في طباعة السند: {e}")
            return False
    
    def cleanup_temp_files(self):
        """حذف الملفات المؤقتة"""
        for file_path in self.temp_files:
            try:
                if os.path.exists(file_path):
                    os.unlink(file_path)
            except:
                pass
        self.temp_files.clear()

# إنشاء مثيل عام للاستخدام
print_manager = PrintManager()