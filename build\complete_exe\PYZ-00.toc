('E:\\desktop_stores_app\\build\\complete_exe\\PYZ-00.pyz',
 [('PIL',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\PIL\\__init__.py',
   'PYMODULE-1'),
  ('PIL.AvifImagePlugin',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\PIL\\AvifImagePlugin.py',
   'PYMODULE-1'),
  ('PIL.BlpImagePlugin',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\PIL\\BlpImagePlugin.py',
   'PYMODULE-1'),
  ('PIL.BmpImagePlugin',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\PIL\\BmpImagePlugin.py',
   'PYMODULE-1'),
  ('PIL.BufrStubImagePlugin',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\PIL\\BufrStubImagePlugin.py',
   'PYMODULE-1'),
  ('PIL.CurImagePlugin',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\PIL\\CurImagePlugin.py',
   'PYMODULE-1'),
  ('PIL.DcxImagePlugin',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\PIL\\DcxImagePlugin.py',
   'PYMODULE-1'),
  ('PIL.DdsImagePlugin',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\PIL\\DdsImagePlugin.py',
   'PYMODULE-1'),
  ('PIL.EpsImagePlugin',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\PIL\\EpsImagePlugin.py',
   'PYMODULE-1'),
  ('PIL.ExifTags',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\PIL\\ExifTags.py',
   'PYMODULE-1'),
  ('PIL.FitsImagePlugin',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\PIL\\FitsImagePlugin.py',
   'PYMODULE-1'),
  ('PIL.FliImagePlugin',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\PIL\\FliImagePlugin.py',
   'PYMODULE-1'),
  ('PIL.FpxImagePlugin',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\PIL\\FpxImagePlugin.py',
   'PYMODULE-1'),
  ('PIL.FtexImagePlugin',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\PIL\\FtexImagePlugin.py',
   'PYMODULE-1'),
  ('PIL.GbrImagePlugin',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\PIL\\GbrImagePlugin.py',
   'PYMODULE-1'),
  ('PIL.GifImagePlugin',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\PIL\\GifImagePlugin.py',
   'PYMODULE-1'),
  ('PIL.GimpGradientFile',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\PIL\\GimpGradientFile.py',
   'PYMODULE-1'),
  ('PIL.GimpPaletteFile',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\PIL\\GimpPaletteFile.py',
   'PYMODULE-1'),
  ('PIL.GribStubImagePlugin',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\PIL\\GribStubImagePlugin.py',
   'PYMODULE-1'),
  ('PIL.Hdf5StubImagePlugin',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\PIL\\Hdf5StubImagePlugin.py',
   'PYMODULE-1'),
  ('PIL.IcnsImagePlugin',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\PIL\\IcnsImagePlugin.py',
   'PYMODULE-1'),
  ('PIL.IcoImagePlugin',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\PIL\\IcoImagePlugin.py',
   'PYMODULE-1'),
  ('PIL.ImImagePlugin',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\PIL\\ImImagePlugin.py',
   'PYMODULE-1'),
  ('PIL.Image',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\PIL\\Image.py',
   'PYMODULE-1'),
  ('PIL.ImageChops',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\PIL\\ImageChops.py',
   'PYMODULE-1'),
  ('PIL.ImageCms',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\PIL\\ImageCms.py',
   'PYMODULE-1'),
  ('PIL.ImageColor',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\PIL\\ImageColor.py',
   'PYMODULE-1'),
  ('PIL.ImageDraw',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\PIL\\ImageDraw.py',
   'PYMODULE-1'),
  ('PIL.ImageDraw2',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\PIL\\ImageDraw2.py',
   'PYMODULE-1'),
  ('PIL.ImageEnhance',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\PIL\\ImageEnhance.py',
   'PYMODULE-1'),
  ('PIL.ImageFile',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\PIL\\ImageFile.py',
   'PYMODULE-1'),
  ('PIL.ImageFilter',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\PIL\\ImageFilter.py',
   'PYMODULE-1'),
  ('PIL.ImageFont',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\PIL\\ImageFont.py',
   'PYMODULE-1'),
  ('PIL.ImageGrab',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\PIL\\ImageGrab.py',
   'PYMODULE-1'),
  ('PIL.ImageMath',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\PIL\\ImageMath.py',
   'PYMODULE-1'),
  ('PIL.ImageMode',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\PIL\\ImageMode.py',
   'PYMODULE-1'),
  ('PIL.ImageOps',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\PIL\\ImageOps.py',
   'PYMODULE-1'),
  ('PIL.ImagePalette',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\PIL\\ImagePalette.py',
   'PYMODULE-1'),
  ('PIL.ImagePath',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\PIL\\ImagePath.py',
   'PYMODULE-1'),
  ('PIL.ImageQt',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\PIL\\ImageQt.py',
   'PYMODULE-1'),
  ('PIL.ImageSequence',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\PIL\\ImageSequence.py',
   'PYMODULE-1'),
  ('PIL.ImageShow',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\PIL\\ImageShow.py',
   'PYMODULE-1'),
  ('PIL.ImageStat',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\PIL\\ImageStat.py',
   'PYMODULE-1'),
  ('PIL.ImageTk',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\PIL\\ImageTk.py',
   'PYMODULE-1'),
  ('PIL.ImageWin',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\PIL\\ImageWin.py',
   'PYMODULE-1'),
  ('PIL.ImtImagePlugin',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\PIL\\ImtImagePlugin.py',
   'PYMODULE-1'),
  ('PIL.IptcImagePlugin',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\PIL\\IptcImagePlugin.py',
   'PYMODULE-1'),
  ('PIL.Jpeg2KImagePlugin',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\PIL\\Jpeg2KImagePlugin.py',
   'PYMODULE-1'),
  ('PIL.JpegImagePlugin',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\PIL\\JpegImagePlugin.py',
   'PYMODULE-1'),
  ('PIL.JpegPresets',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\PIL\\JpegPresets.py',
   'PYMODULE-1'),
  ('PIL.McIdasImagePlugin',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\PIL\\McIdasImagePlugin.py',
   'PYMODULE-1'),
  ('PIL.MicImagePlugin',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\PIL\\MicImagePlugin.py',
   'PYMODULE-1'),
  ('PIL.MpegImagePlugin',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\PIL\\MpegImagePlugin.py',
   'PYMODULE-1'),
  ('PIL.MpoImagePlugin',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\PIL\\MpoImagePlugin.py',
   'PYMODULE-1'),
  ('PIL.MspImagePlugin',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\PIL\\MspImagePlugin.py',
   'PYMODULE-1'),
  ('PIL.PaletteFile',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\PIL\\PaletteFile.py',
   'PYMODULE-1'),
  ('PIL.PalmImagePlugin',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\PIL\\PalmImagePlugin.py',
   'PYMODULE-1'),
  ('PIL.PcdImagePlugin',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\PIL\\PcdImagePlugin.py',
   'PYMODULE-1'),
  ('PIL.PcxImagePlugin',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\PIL\\PcxImagePlugin.py',
   'PYMODULE-1'),
  ('PIL.PdfImagePlugin',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\PIL\\PdfImagePlugin.py',
   'PYMODULE-1'),
  ('PIL.PdfParser',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\PIL\\PdfParser.py',
   'PYMODULE-1'),
  ('PIL.PixarImagePlugin',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\PIL\\PixarImagePlugin.py',
   'PYMODULE-1'),
  ('PIL.PngImagePlugin',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\PIL\\PngImagePlugin.py',
   'PYMODULE-1'),
  ('PIL.PpmImagePlugin',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\PIL\\PpmImagePlugin.py',
   'PYMODULE-1'),
  ('PIL.PsdImagePlugin',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\PIL\\PsdImagePlugin.py',
   'PYMODULE-1'),
  ('PIL.QoiImagePlugin',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\PIL\\QoiImagePlugin.py',
   'PYMODULE-1'),
  ('PIL.SgiImagePlugin',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\PIL\\SgiImagePlugin.py',
   'PYMODULE-1'),
  ('PIL.SpiderImagePlugin',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\PIL\\SpiderImagePlugin.py',
   'PYMODULE-1'),
  ('PIL.SunImagePlugin',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\PIL\\SunImagePlugin.py',
   'PYMODULE-1'),
  ('PIL.TgaImagePlugin',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\PIL\\TgaImagePlugin.py',
   'PYMODULE-1'),
  ('PIL.TiffImagePlugin',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\PIL\\TiffImagePlugin.py',
   'PYMODULE-1'),
  ('PIL.TiffTags',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\PIL\\TiffTags.py',
   'PYMODULE-1'),
  ('PIL.WebPImagePlugin',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\PIL\\WebPImagePlugin.py',
   'PYMODULE-1'),
  ('PIL.WmfImagePlugin',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\PIL\\WmfImagePlugin.py',
   'PYMODULE-1'),
  ('PIL.XVThumbImagePlugin',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\PIL\\XVThumbImagePlugin.py',
   'PYMODULE-1'),
  ('PIL.XbmImagePlugin',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\PIL\\XbmImagePlugin.py',
   'PYMODULE-1'),
  ('PIL.XpmImagePlugin',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\PIL\\XpmImagePlugin.py',
   'PYMODULE-1'),
  ('PIL._binary',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\PIL\\_binary.py',
   'PYMODULE-1'),
  ('PIL._deprecate',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\PIL\\_deprecate.py',
   'PYMODULE-1'),
  ('PIL._typing',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\PIL\\_typing.py',
   'PYMODULE-1'),
  ('PIL._util',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\PIL\\_util.py',
   'PYMODULE-1'),
  ('PIL._version',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\PIL\\_version.py',
   'PYMODULE-1'),
  ('PIL.features',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\PIL\\features.py',
   'PYMODULE-1'),
  ('__future__',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\__future__.py',
   'PYMODULE-1'),
  ('_aix_support',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_aix_support.py',
   'PYMODULE-1'),
  ('_colorize',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_colorize.py',
   'PYMODULE-1'),
  ('_compat_pickle',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_compat_pickle.py',
   'PYMODULE-1'),
  ('_compression',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_compression.py',
   'PYMODULE-1'),
  ('_ios_support',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_ios_support.py',
   'PYMODULE-1'),
  ('_markupbase',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_markupbase.py',
   'PYMODULE-1'),
  ('_opcode_metadata',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_opcode_metadata.py',
   'PYMODULE-1'),
  ('_py_abc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_py_abc.py',
   'PYMODULE-1'),
  ('_pydatetime',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_pydatetime.py',
   'PYMODULE-1'),
  ('_pydecimal',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_pydecimal.py',
   'PYMODULE-1'),
  ('_pyi_rth_utils',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\__init__.py',
   'PYMODULE-1'),
  ('_pyi_rth_utils._win32',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\_win32.py',
   'PYMODULE-1'),
  ('_pyi_rth_utils.tempfile',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\tempfile.py',
   'PYMODULE-1'),
  ('_pyrepl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_pyrepl\\__init__.py',
   'PYMODULE-1'),
  ('_pyrepl.pager',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_pyrepl\\pager.py',
   'PYMODULE-1'),
  ('_strptime',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_strptime.py',
   'PYMODULE-1'),
  ('_threading_local',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_threading_local.py',
   'PYMODULE-1'),
  ('activity_monitor',
   'E:\\desktop_stores_app\\activity_monitor.py',
   'PYMODULE-1'),
  ('argparse',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\argparse.py',
   'PYMODULE-1'),
  ('ast',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\ast.py',
   'PYMODULE-1'),
  ('asyncio',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\__init__.py',
   'PYMODULE-1'),
  ('asyncio.base_events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\base_events.py',
   'PYMODULE-1'),
  ('asyncio.base_futures',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\base_futures.py',
   'PYMODULE-1'),
  ('asyncio.base_subprocess',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\base_subprocess.py',
   'PYMODULE-1'),
  ('asyncio.base_tasks',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\base_tasks.py',
   'PYMODULE-1'),
  ('asyncio.constants',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\constants.py',
   'PYMODULE-1'),
  ('asyncio.coroutines',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\coroutines.py',
   'PYMODULE-1'),
  ('asyncio.events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\events.py',
   'PYMODULE-1'),
  ('asyncio.exceptions',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\exceptions.py',
   'PYMODULE-1'),
  ('asyncio.format_helpers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\format_helpers.py',
   'PYMODULE-1'),
  ('asyncio.futures',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\futures.py',
   'PYMODULE-1'),
  ('asyncio.locks',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\locks.py',
   'PYMODULE-1'),
  ('asyncio.log',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\log.py',
   'PYMODULE-1'),
  ('asyncio.mixins',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\mixins.py',
   'PYMODULE-1'),
  ('asyncio.proactor_events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\proactor_events.py',
   'PYMODULE-1'),
  ('asyncio.protocols',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\protocols.py',
   'PYMODULE-1'),
  ('asyncio.queues',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\queues.py',
   'PYMODULE-1'),
  ('asyncio.runners',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\runners.py',
   'PYMODULE-1'),
  ('asyncio.selector_events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\selector_events.py',
   'PYMODULE-1'),
  ('asyncio.sslproto',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\sslproto.py',
   'PYMODULE-1'),
  ('asyncio.staggered',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\staggered.py',
   'PYMODULE-1'),
  ('asyncio.streams',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\streams.py',
   'PYMODULE-1'),
  ('asyncio.subprocess',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\subprocess.py',
   'PYMODULE-1'),
  ('asyncio.taskgroups',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\taskgroups.py',
   'PYMODULE-1'),
  ('asyncio.tasks',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\tasks.py',
   'PYMODULE-1'),
  ('asyncio.threads',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\threads.py',
   'PYMODULE-1'),
  ('asyncio.timeouts',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\timeouts.py',
   'PYMODULE-1'),
  ('asyncio.transports',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\transports.py',
   'PYMODULE-1'),
  ('asyncio.trsock',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\trsock.py',
   'PYMODULE-1'),
  ('asyncio.unix_events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\unix_events.py',
   'PYMODULE-1'),
  ('asyncio.windows_events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\windows_events.py',
   'PYMODULE-1'),
  ('asyncio.windows_utils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\windows_utils.py',
   'PYMODULE-1'),
  ('auth_manager', 'E:\\desktop_stores_app\\auth_manager.py', 'PYMODULE-1'),
  ('base64',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\base64.py',
   'PYMODULE-1'),
  ('bcrypt',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\bcrypt\\__init__.py',
   'PYMODULE-1'),
  ('bdb',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\bdb.py',
   'PYMODULE-1'),
  ('bisect',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\bisect.py',
   'PYMODULE-1'),
  ('bz2',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\bz2.py',
   'PYMODULE-1'),
  ('calendar',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\calendar.py',
   'PYMODULE-1'),
  ('charset_normalizer',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\charset_normalizer\\__init__.py',
   'PYMODULE-1'),
  ('charset_normalizer.api',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\charset_normalizer\\api.py',
   'PYMODULE-1'),
  ('charset_normalizer.cd',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\charset_normalizer\\cd.py',
   'PYMODULE-1'),
  ('charset_normalizer.constant',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\charset_normalizer\\constant.py',
   'PYMODULE-1'),
  ('charset_normalizer.legacy',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\charset_normalizer\\legacy.py',
   'PYMODULE-1'),
  ('charset_normalizer.models',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\charset_normalizer\\models.py',
   'PYMODULE-1'),
  ('charset_normalizer.utils',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\charset_normalizer\\utils.py',
   'PYMODULE-1'),
  ('charset_normalizer.version',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\charset_normalizer\\version.py',
   'PYMODULE-1'),
  ('cmd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\cmd.py',
   'PYMODULE-1'),
  ('code',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\code.py',
   'PYMODULE-1'),
  ('codeop',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\codeop.py',
   'PYMODULE-1'),
  ('colorsys',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\colorsys.py',
   'PYMODULE-1'),
  ('concurrent',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\concurrent\\__init__.py',
   'PYMODULE-1'),
  ('concurrent.futures',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\concurrent\\futures\\__init__.py',
   'PYMODULE-1'),
  ('concurrent.futures._base',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\concurrent\\futures\\_base.py',
   'PYMODULE-1'),
  ('concurrent.futures.process',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\concurrent\\futures\\process.py',
   'PYMODULE-1'),
  ('concurrent.futures.thread',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\concurrent\\futures\\thread.py',
   'PYMODULE-1'),
  ('config', 'E:\\desktop_stores_app\\config.py', 'PYMODULE-1'),
  ('contextlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\contextlib.py',
   'PYMODULE-1'),
  ('contextvars',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\contextvars.py',
   'PYMODULE-1'),
  ('contourpy',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\contourpy\\__init__.py',
   'PYMODULE-1'),
  ('contourpy._version',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\contourpy\\_version.py',
   'PYMODULE-1'),
  ('contourpy.array',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\contourpy\\array.py',
   'PYMODULE-1'),
  ('contourpy.chunk',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\contourpy\\chunk.py',
   'PYMODULE-1'),
  ('contourpy.convert',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\contourpy\\convert.py',
   'PYMODULE-1'),
  ('contourpy.dechunk',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\contourpy\\dechunk.py',
   'PYMODULE-1'),
  ('contourpy.enum_util',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\contourpy\\enum_util.py',
   'PYMODULE-1'),
  ('contourpy.typecheck',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\contourpy\\typecheck.py',
   'PYMODULE-1'),
  ('contourpy.types',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\contourpy\\types.py',
   'PYMODULE-1'),
  ('copy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\copy.py',
   'PYMODULE-1'),
  ('csv',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\csv.py',
   'PYMODULE-1'),
  ('ctypes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\ctypes\\__init__.py',
   'PYMODULE-1'),
  ('ctypes._aix',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\ctypes\\_aix.py',
   'PYMODULE-1'),
  ('ctypes._endian',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\ctypes\\_endian.py',
   'PYMODULE-1'),
  ('ctypes.macholib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\ctypes\\macholib\\__init__.py',
   'PYMODULE-1'),
  ('ctypes.macholib.dyld',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\ctypes\\macholib\\dyld.py',
   'PYMODULE-1'),
  ('ctypes.macholib.dylib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\ctypes\\macholib\\dylib.py',
   'PYMODULE-1'),
  ('ctypes.macholib.framework',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\ctypes\\macholib\\framework.py',
   'PYMODULE-1'),
  ('ctypes.util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\ctypes\\util.py',
   'PYMODULE-1'),
  ('ctypes.wintypes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\ctypes\\wintypes.py',
   'PYMODULE-1'),
  ('cycler',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\cycler\\__init__.py',
   'PYMODULE-1'),
  ('database', 'E:\\desktop_stores_app\\database.py', 'PYMODULE-1'),
  ('dataclasses',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\dataclasses.py',
   'PYMODULE-1'),
  ('datetime',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\datetime.py',
   'PYMODULE-1'),
  ('dateutil',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\dateutil\\__init__.py',
   'PYMODULE-1'),
  ('dateutil._common',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\dateutil\\_common.py',
   'PYMODULE-1'),
  ('dateutil._version',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\dateutil\\_version.py',
   'PYMODULE-1'),
  ('dateutil.easter',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\dateutil\\easter.py',
   'PYMODULE-1'),
  ('dateutil.parser',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\dateutil\\parser\\__init__.py',
   'PYMODULE-1'),
  ('dateutil.parser._parser',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\dateutil\\parser\\_parser.py',
   'PYMODULE-1'),
  ('dateutil.parser.isoparser',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\dateutil\\parser\\isoparser.py',
   'PYMODULE-1'),
  ('dateutil.relativedelta',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\dateutil\\relativedelta.py',
   'PYMODULE-1'),
  ('dateutil.rrule',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\dateutil\\rrule.py',
   'PYMODULE-1'),
  ('dateutil.tz',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\dateutil\\tz\\__init__.py',
   'PYMODULE-1'),
  ('dateutil.tz._common',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\dateutil\\tz\\_common.py',
   'PYMODULE-1'),
  ('dateutil.tz._factories',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\dateutil\\tz\\_factories.py',
   'PYMODULE-1'),
  ('dateutil.tz.tz',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\dateutil\\tz\\tz.py',
   'PYMODULE-1'),
  ('dateutil.tz.win',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\dateutil\\tz\\win.py',
   'PYMODULE-1'),
  ('dateutil.zoneinfo',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\dateutil\\zoneinfo\\__init__.py',
   'PYMODULE-1'),
  ('decimal',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\decimal.py',
   'PYMODULE-1'),
  ('difflib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\difflib.py',
   'PYMODULE-1'),
  ('dis',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\dis.py',
   'PYMODULE-1'),
  ('doctest',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\doctest.py',
   'PYMODULE-1'),
  ('email',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\__init__.py',
   'PYMODULE-1'),
  ('email._encoded_words',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\_encoded_words.py',
   'PYMODULE-1'),
  ('email._header_value_parser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\_header_value_parser.py',
   'PYMODULE-1'),
  ('email._parseaddr',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\_parseaddr.py',
   'PYMODULE-1'),
  ('email._policybase',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\_policybase.py',
   'PYMODULE-1'),
  ('email.base64mime',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\base64mime.py',
   'PYMODULE-1'),
  ('email.charset',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\charset.py',
   'PYMODULE-1'),
  ('email.contentmanager',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\contentmanager.py',
   'PYMODULE-1'),
  ('email.encoders',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\encoders.py',
   'PYMODULE-1'),
  ('email.errors',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\errors.py',
   'PYMODULE-1'),
  ('email.feedparser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\feedparser.py',
   'PYMODULE-1'),
  ('email.generator',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\generator.py',
   'PYMODULE-1'),
  ('email.header',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\header.py',
   'PYMODULE-1'),
  ('email.headerregistry',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\headerregistry.py',
   'PYMODULE-1'),
  ('email.iterators',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\iterators.py',
   'PYMODULE-1'),
  ('email.message',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\message.py',
   'PYMODULE-1'),
  ('email.parser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\parser.py',
   'PYMODULE-1'),
  ('email.policy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\policy.py',
   'PYMODULE-1'),
  ('email.quoprimime',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\quoprimime.py',
   'PYMODULE-1'),
  ('email.utils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\utils.py',
   'PYMODULE-1'),
  ('et_xmlfile',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\et_xmlfile\\__init__.py',
   'PYMODULE-1'),
  ('et_xmlfile.incremental_tree',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\et_xmlfile\\incremental_tree.py',
   'PYMODULE-1'),
  ('et_xmlfile.xmlfile',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\et_xmlfile\\xmlfile.py',
   'PYMODULE-1'),
  ('fileinput',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\fileinput.py',
   'PYMODULE-1'),
  ('fnmatch',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\fnmatch.py',
   'PYMODULE-1'),
  ('font_manager', 'E:\\desktop_stores_app\\font_manager.py', 'PYMODULE-1'),
  ('fractions',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\fractions.py',
   'PYMODULE-1'),
  ('ftplib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\ftplib.py',
   'PYMODULE-1'),
  ('getopt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\getopt.py',
   'PYMODULE-1'),
  ('getpass',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\getpass.py',
   'PYMODULE-1'),
  ('gettext',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\gettext.py',
   'PYMODULE-1'),
  ('glob',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\glob.py',
   'PYMODULE-1'),
  ('gzip',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\gzip.py',
   'PYMODULE-1'),
  ('hashlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\hashlib.py',
   'PYMODULE-1'),
  ('hmac',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\hmac.py',
   'PYMODULE-1'),
  ('html',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\html\\__init__.py',
   'PYMODULE-1'),
  ('html.entities',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\html\\entities.py',
   'PYMODULE-1'),
  ('html.parser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\html\\parser.py',
   'PYMODULE-1'),
  ('http',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\http\\__init__.py',
   'PYMODULE-1'),
  ('http.client',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\http\\client.py',
   'PYMODULE-1'),
  ('http.cookiejar',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\http\\cookiejar.py',
   'PYMODULE-1'),
  ('http.server',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\http\\server.py',
   'PYMODULE-1'),
  ('importlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\__init__.py',
   'PYMODULE-1'),
  ('importlib._abc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\_abc.py',
   'PYMODULE-1'),
  ('importlib._bootstrap',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\_bootstrap.py',
   'PYMODULE-1'),
  ('importlib._bootstrap_external',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\_bootstrap_external.py',
   'PYMODULE-1'),
  ('importlib.abc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\abc.py',
   'PYMODULE-1'),
  ('importlib.machinery',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\machinery.py',
   'PYMODULE-1'),
  ('importlib.metadata',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\metadata\\__init__.py',
   'PYMODULE-1'),
  ('importlib.metadata._adapters',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE-1'),
  ('importlib.metadata._collections',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\metadata\\_collections.py',
   'PYMODULE-1'),
  ('importlib.metadata._functools',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\metadata\\_functools.py',
   'PYMODULE-1'),
  ('importlib.metadata._itertools',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE-1'),
  ('importlib.metadata._meta',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\metadata\\_meta.py',
   'PYMODULE-1'),
  ('importlib.metadata._text',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\metadata\\_text.py',
   'PYMODULE-1'),
  ('importlib.readers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\readers.py',
   'PYMODULE-1'),
  ('importlib.resources',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\resources\\__init__.py',
   'PYMODULE-1'),
  ('importlib.resources._adapters',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\resources\\_adapters.py',
   'PYMODULE-1'),
  ('importlib.resources._common',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\resources\\_common.py',
   'PYMODULE-1'),
  ('importlib.resources._functional',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\resources\\_functional.py',
   'PYMODULE-1'),
  ('importlib.resources._itertools',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\resources\\_itertools.py',
   'PYMODULE-1'),
  ('importlib.resources.abc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\resources\\abc.py',
   'PYMODULE-1'),
  ('importlib.resources.readers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\resources\\readers.py',
   'PYMODULE-1'),
  ('importlib.util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\util.py',
   'PYMODULE-1'),
  ('inspect',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\inspect.py',
   'PYMODULE-1'),
  ('ipaddress',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\ipaddress.py',
   'PYMODULE-1'),
  ('json',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\json\\__init__.py',
   'PYMODULE-1'),
  ('json.decoder',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\json\\decoder.py',
   'PYMODULE-1'),
  ('json.encoder',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\json\\encoder.py',
   'PYMODULE-1'),
  ('json.scanner',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\json\\scanner.py',
   'PYMODULE-1'),
  ('kiwisolver',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\kiwisolver\\__init__.py',
   'PYMODULE-1'),
  ('kiwisolver.exceptions',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\kiwisolver\\exceptions.py',
   'PYMODULE-1'),
  ('logging',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\logging\\__init__.py',
   'PYMODULE-1'),
  ('logging.handlers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\logging\\handlers.py',
   'PYMODULE-1'),
  ('lzma',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\lzma.py',
   'PYMODULE-1'),
  ('matplotlib',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\matplotlib\\__init__.py',
   'PYMODULE-1'),
  ('matplotlib._afm',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\matplotlib\\_afm.py',
   'PYMODULE-1'),
  ('matplotlib._api',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\matplotlib\\_api\\__init__.py',
   'PYMODULE-1'),
  ('matplotlib._api.deprecation',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\matplotlib\\_api\\deprecation.py',
   'PYMODULE-1'),
  ('matplotlib._blocking_input',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\matplotlib\\_blocking_input.py',
   'PYMODULE-1'),
  ('matplotlib._cm',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\matplotlib\\_cm.py',
   'PYMODULE-1'),
  ('matplotlib._cm_bivar',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\matplotlib\\_cm_bivar.py',
   'PYMODULE-1'),
  ('matplotlib._cm_listed',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\matplotlib\\_cm_listed.py',
   'PYMODULE-1'),
  ('matplotlib._cm_multivar',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\matplotlib\\_cm_multivar.py',
   'PYMODULE-1'),
  ('matplotlib._color_data',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\matplotlib\\_color_data.py',
   'PYMODULE-1'),
  ('matplotlib._constrained_layout',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\matplotlib\\_constrained_layout.py',
   'PYMODULE-1'),
  ('matplotlib._docstring',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\matplotlib\\_docstring.py',
   'PYMODULE-1'),
  ('matplotlib._enums',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\matplotlib\\_enums.py',
   'PYMODULE-1'),
  ('matplotlib._fontconfig_pattern',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\matplotlib\\_fontconfig_pattern.py',
   'PYMODULE-1'),
  ('matplotlib._layoutgrid',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\matplotlib\\_layoutgrid.py',
   'PYMODULE-1'),
  ('matplotlib._mathtext',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\matplotlib\\_mathtext.py',
   'PYMODULE-1'),
  ('matplotlib._mathtext_data',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\matplotlib\\_mathtext_data.py',
   'PYMODULE-1'),
  ('matplotlib._pylab_helpers',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\matplotlib\\_pylab_helpers.py',
   'PYMODULE-1'),
  ('matplotlib._text_helpers',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\matplotlib\\_text_helpers.py',
   'PYMODULE-1'),
  ('matplotlib._tight_bbox',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\matplotlib\\_tight_bbox.py',
   'PYMODULE-1'),
  ('matplotlib._tight_layout',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\matplotlib\\_tight_layout.py',
   'PYMODULE-1'),
  ('matplotlib._version',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\matplotlib\\_version.py',
   'PYMODULE-1'),
  ('matplotlib.artist',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\matplotlib\\artist.py',
   'PYMODULE-1'),
  ('matplotlib.axes',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\matplotlib\\axes\\__init__.py',
   'PYMODULE-1'),
  ('matplotlib.axes._axes',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\matplotlib\\axes\\_axes.py',
   'PYMODULE-1'),
  ('matplotlib.axes._base',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\matplotlib\\axes\\_base.py',
   'PYMODULE-1'),
  ('matplotlib.axes._secondary_axes',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\matplotlib\\axes\\_secondary_axes.py',
   'PYMODULE-1'),
  ('matplotlib.axis',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\matplotlib\\axis.py',
   'PYMODULE-1'),
  ('matplotlib.backend_bases',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\matplotlib\\backend_bases.py',
   'PYMODULE-1'),
  ('matplotlib.backend_managers',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\matplotlib\\backend_managers.py',
   'PYMODULE-1'),
  ('matplotlib.backend_tools',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\matplotlib\\backend_tools.py',
   'PYMODULE-1'),
  ('matplotlib.backends',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\matplotlib\\backends\\__init__.py',
   'PYMODULE-1'),
  ('matplotlib.backends._backend_tk',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\matplotlib\\backends\\_backend_tk.py',
   'PYMODULE-1'),
  ('matplotlib.backends.backend_agg',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\matplotlib\\backends\\backend_agg.py',
   'PYMODULE-1'),
  ('matplotlib.backends.backend_tkagg',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\matplotlib\\backends\\backend_tkagg.py',
   'PYMODULE-1'),
  ('matplotlib.backends.backend_webagg',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\matplotlib\\backends\\backend_webagg.py',
   'PYMODULE-1'),
  ('matplotlib.backends.backend_webagg_core',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\matplotlib\\backends\\backend_webagg_core.py',
   'PYMODULE-1'),
  ('matplotlib.backends.registry',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\matplotlib\\backends\\registry.py',
   'PYMODULE-1'),
  ('matplotlib.bezier',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\matplotlib\\bezier.py',
   'PYMODULE-1'),
  ('matplotlib.category',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\matplotlib\\category.py',
   'PYMODULE-1'),
  ('matplotlib.cbook',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\matplotlib\\cbook.py',
   'PYMODULE-1'),
  ('matplotlib.cm',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\matplotlib\\cm.py',
   'PYMODULE-1'),
  ('matplotlib.collections',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\matplotlib\\collections.py',
   'PYMODULE-1'),
  ('matplotlib.colorbar',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\matplotlib\\colorbar.py',
   'PYMODULE-1'),
  ('matplotlib.colorizer',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\matplotlib\\colorizer.py',
   'PYMODULE-1'),
  ('matplotlib.colors',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\matplotlib\\colors.py',
   'PYMODULE-1'),
  ('matplotlib.container',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\matplotlib\\container.py',
   'PYMODULE-1'),
  ('matplotlib.contour',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\matplotlib\\contour.py',
   'PYMODULE-1'),
  ('matplotlib.dates',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\matplotlib\\dates.py',
   'PYMODULE-1'),
  ('matplotlib.dviread',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\matplotlib\\dviread.py',
   'PYMODULE-1'),
  ('matplotlib.figure',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\matplotlib\\figure.py',
   'PYMODULE-1'),
  ('matplotlib.font_manager',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\matplotlib\\font_manager.py',
   'PYMODULE-1'),
  ('matplotlib.gridspec',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\matplotlib\\gridspec.py',
   'PYMODULE-1'),
  ('matplotlib.hatch',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\matplotlib\\hatch.py',
   'PYMODULE-1'),
  ('matplotlib.image',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\matplotlib\\image.py',
   'PYMODULE-1'),
  ('matplotlib.inset',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\matplotlib\\inset.py',
   'PYMODULE-1'),
  ('matplotlib.layout_engine',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\matplotlib\\layout_engine.py',
   'PYMODULE-1'),
  ('matplotlib.legend',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\matplotlib\\legend.py',
   'PYMODULE-1'),
  ('matplotlib.legend_handler',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\matplotlib\\legend_handler.py',
   'PYMODULE-1'),
  ('matplotlib.lines',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\matplotlib\\lines.py',
   'PYMODULE-1'),
  ('matplotlib.markers',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\matplotlib\\markers.py',
   'PYMODULE-1'),
  ('matplotlib.mathtext',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\matplotlib\\mathtext.py',
   'PYMODULE-1'),
  ('matplotlib.mlab',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\matplotlib\\mlab.py',
   'PYMODULE-1'),
  ('matplotlib.offsetbox',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\matplotlib\\offsetbox.py',
   'PYMODULE-1'),
  ('matplotlib.patches',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\matplotlib\\patches.py',
   'PYMODULE-1'),
  ('matplotlib.path',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\matplotlib\\path.py',
   'PYMODULE-1'),
  ('matplotlib.patheffects',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\matplotlib\\patheffects.py',
   'PYMODULE-1'),
  ('matplotlib.projections',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\matplotlib\\projections\\__init__.py',
   'PYMODULE-1'),
  ('matplotlib.projections.geo',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\matplotlib\\projections\\geo.py',
   'PYMODULE-1'),
  ('matplotlib.projections.polar',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\matplotlib\\projections\\polar.py',
   'PYMODULE-1'),
  ('matplotlib.pylab',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\matplotlib\\pylab.py',
   'PYMODULE-1'),
  ('matplotlib.pyplot',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\matplotlib\\pyplot.py',
   'PYMODULE-1'),
  ('matplotlib.quiver',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\matplotlib\\quiver.py',
   'PYMODULE-1'),
  ('matplotlib.rcsetup',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\matplotlib\\rcsetup.py',
   'PYMODULE-1'),
  ('matplotlib.scale',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\matplotlib\\scale.py',
   'PYMODULE-1'),
  ('matplotlib.spines',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\matplotlib\\spines.py',
   'PYMODULE-1'),
  ('matplotlib.stackplot',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\matplotlib\\stackplot.py',
   'PYMODULE-1'),
  ('matplotlib.streamplot',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\matplotlib\\streamplot.py',
   'PYMODULE-1'),
  ('matplotlib.style',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\matplotlib\\style\\__init__.py',
   'PYMODULE-1'),
  ('matplotlib.style.core',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\matplotlib\\style\\core.py',
   'PYMODULE-1'),
  ('matplotlib.table',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\matplotlib\\table.py',
   'PYMODULE-1'),
  ('matplotlib.texmanager',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\matplotlib\\texmanager.py',
   'PYMODULE-1'),
  ('matplotlib.text',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\matplotlib\\text.py',
   'PYMODULE-1'),
  ('matplotlib.textpath',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\matplotlib\\textpath.py',
   'PYMODULE-1'),
  ('matplotlib.ticker',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\matplotlib\\ticker.py',
   'PYMODULE-1'),
  ('matplotlib.transforms',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\matplotlib\\transforms.py',
   'PYMODULE-1'),
  ('matplotlib.tri',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\matplotlib\\tri\\__init__.py',
   'PYMODULE-1'),
  ('matplotlib.tri._triangulation',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\matplotlib\\tri\\_triangulation.py',
   'PYMODULE-1'),
  ('matplotlib.tri._tricontour',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\matplotlib\\tri\\_tricontour.py',
   'PYMODULE-1'),
  ('matplotlib.tri._trifinder',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\matplotlib\\tri\\_trifinder.py',
   'PYMODULE-1'),
  ('matplotlib.tri._triinterpolate',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\matplotlib\\tri\\_triinterpolate.py',
   'PYMODULE-1'),
  ('matplotlib.tri._tripcolor',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\matplotlib\\tri\\_tripcolor.py',
   'PYMODULE-1'),
  ('matplotlib.tri._triplot',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\matplotlib\\tri\\_triplot.py',
   'PYMODULE-1'),
  ('matplotlib.tri._trirefine',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\matplotlib\\tri\\_trirefine.py',
   'PYMODULE-1'),
  ('matplotlib.tri._tritools',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\matplotlib\\tri\\_tritools.py',
   'PYMODULE-1'),
  ('matplotlib.typing',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\matplotlib\\typing.py',
   'PYMODULE-1'),
  ('matplotlib.units',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\matplotlib\\units.py',
   'PYMODULE-1'),
  ('matplotlib.widgets',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\matplotlib\\widgets.py',
   'PYMODULE-1'),
  ('mimetypes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\mimetypes.py',
   'PYMODULE-1'),
  ('models', 'E:\\desktop_stores_app\\models.py', 'PYMODULE-1'),
  ('mpl_toolkits', '-', 'PYMODULE-1'),
  ('mpl_toolkits.mplot3d',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\mpl_toolkits\\mplot3d\\__init__.py',
   'PYMODULE-1'),
  ('mpl_toolkits.mplot3d.art3d',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\mpl_toolkits\\mplot3d\\art3d.py',
   'PYMODULE-1'),
  ('mpl_toolkits.mplot3d.axes3d',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\mpl_toolkits\\mplot3d\\axes3d.py',
   'PYMODULE-1'),
  ('mpl_toolkits.mplot3d.axis3d',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\mpl_toolkits\\mplot3d\\axis3d.py',
   'PYMODULE-1'),
  ('mpl_toolkits.mplot3d.proj3d',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\mpl_toolkits\\mplot3d\\proj3d.py',
   'PYMODULE-1'),
  ('multiprocessing',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\__init__.py',
   'PYMODULE-1'),
  ('multiprocessing.connection',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\connection.py',
   'PYMODULE-1'),
  ('multiprocessing.context',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\context.py',
   'PYMODULE-1'),
  ('multiprocessing.dummy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE-1'),
  ('multiprocessing.dummy.connection',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE-1'),
  ('multiprocessing.forkserver',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\forkserver.py',
   'PYMODULE-1'),
  ('multiprocessing.heap',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\heap.py',
   'PYMODULE-1'),
  ('multiprocessing.managers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\managers.py',
   'PYMODULE-1'),
  ('multiprocessing.pool',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\pool.py',
   'PYMODULE-1'),
  ('multiprocessing.popen_fork',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\popen_fork.py',
   'PYMODULE-1'),
  ('multiprocessing.popen_forkserver',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE-1'),
  ('multiprocessing.popen_spawn_posix',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE-1'),
  ('multiprocessing.popen_spawn_win32',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE-1'),
  ('multiprocessing.process',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\process.py',
   'PYMODULE-1'),
  ('multiprocessing.queues',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\queues.py',
   'PYMODULE-1'),
  ('multiprocessing.reduction',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\reduction.py',
   'PYMODULE-1'),
  ('multiprocessing.resource_sharer',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE-1'),
  ('multiprocessing.resource_tracker',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE-1'),
  ('multiprocessing.shared_memory',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\shared_memory.py',
   'PYMODULE-1'),
  ('multiprocessing.sharedctypes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE-1'),
  ('multiprocessing.spawn',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\spawn.py',
   'PYMODULE-1'),
  ('multiprocessing.synchronize',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\synchronize.py',
   'PYMODULE-1'),
  ('multiprocessing.util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\util.py',
   'PYMODULE-1'),
  ('netrc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\netrc.py',
   'PYMODULE-1'),
  ('nturl2path',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\nturl2path.py',
   'PYMODULE-1'),
  ('numbers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\numbers.py',
   'PYMODULE-1'),
  ('numpy',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\numpy\\__init__.py',
   'PYMODULE-1'),
  ('numpy.__config__',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\numpy\\__config__.py',
   'PYMODULE-1'),
  ('numpy._array_api_info',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\numpy\\_array_api_info.py',
   'PYMODULE-1'),
  ('numpy._core',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\numpy\\_core\\__init__.py',
   'PYMODULE-1'),
  ('numpy._core._add_newdocs',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\numpy\\_core\\_add_newdocs.py',
   'PYMODULE-1'),
  ('numpy._core._add_newdocs_scalars',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\numpy\\_core\\_add_newdocs_scalars.py',
   'PYMODULE-1'),
  ('numpy._core._asarray',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\numpy\\_core\\_asarray.py',
   'PYMODULE-1'),
  ('numpy._core._dtype',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\numpy\\_core\\_dtype.py',
   'PYMODULE-1'),
  ('numpy._core._dtype_ctypes',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\numpy\\_core\\_dtype_ctypes.py',
   'PYMODULE-1'),
  ('numpy._core._exceptions',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\numpy\\_core\\_exceptions.py',
   'PYMODULE-1'),
  ('numpy._core._internal',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\numpy\\_core\\_internal.py',
   'PYMODULE-1'),
  ('numpy._core._machar',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\numpy\\_core\\_machar.py',
   'PYMODULE-1'),
  ('numpy._core._methods',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\numpy\\_core\\_methods.py',
   'PYMODULE-1'),
  ('numpy._core._string_helpers',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\numpy\\_core\\_string_helpers.py',
   'PYMODULE-1'),
  ('numpy._core._type_aliases',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\numpy\\_core\\_type_aliases.py',
   'PYMODULE-1'),
  ('numpy._core._ufunc_config',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\numpy\\_core\\_ufunc_config.py',
   'PYMODULE-1'),
  ('numpy._core.arrayprint',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\numpy\\_core\\arrayprint.py',
   'PYMODULE-1'),
  ('numpy._core.defchararray',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\numpy\\_core\\defchararray.py',
   'PYMODULE-1'),
  ('numpy._core.einsumfunc',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\numpy\\_core\\einsumfunc.py',
   'PYMODULE-1'),
  ('numpy._core.fromnumeric',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\numpy\\_core\\fromnumeric.py',
   'PYMODULE-1'),
  ('numpy._core.function_base',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\numpy\\_core\\function_base.py',
   'PYMODULE-1'),
  ('numpy._core.getlimits',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\numpy\\_core\\getlimits.py',
   'PYMODULE-1'),
  ('numpy._core.memmap',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\numpy\\_core\\memmap.py',
   'PYMODULE-1'),
  ('numpy._core.multiarray',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\numpy\\_core\\multiarray.py',
   'PYMODULE-1'),
  ('numpy._core.numeric',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\numpy\\_core\\numeric.py',
   'PYMODULE-1'),
  ('numpy._core.numerictypes',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\numpy\\_core\\numerictypes.py',
   'PYMODULE-1'),
  ('numpy._core.overrides',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\numpy\\_core\\overrides.py',
   'PYMODULE-1'),
  ('numpy._core.printoptions',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\numpy\\_core\\printoptions.py',
   'PYMODULE-1'),
  ('numpy._core.records',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\numpy\\_core\\records.py',
   'PYMODULE-1'),
  ('numpy._core.shape_base',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\numpy\\_core\\shape_base.py',
   'PYMODULE-1'),
  ('numpy._core.strings',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\numpy\\_core\\strings.py',
   'PYMODULE-1'),
  ('numpy._core.umath',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\numpy\\_core\\umath.py',
   'PYMODULE-1'),
  ('numpy._distributor_init',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\numpy\\_distributor_init.py',
   'PYMODULE-1'),
  ('numpy._expired_attrs_2_0',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\numpy\\_expired_attrs_2_0.py',
   'PYMODULE-1'),
  ('numpy._globals',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\numpy\\_globals.py',
   'PYMODULE-1'),
  ('numpy._pytesttester',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\numpy\\_pytesttester.py',
   'PYMODULE-1'),
  ('numpy._typing',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\numpy\\_typing\\__init__.py',
   'PYMODULE-1'),
  ('numpy._typing._add_docstring',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\numpy\\_typing\\_add_docstring.py',
   'PYMODULE-1'),
  ('numpy._typing._array_like',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\numpy\\_typing\\_array_like.py',
   'PYMODULE-1'),
  ('numpy._typing._char_codes',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\numpy\\_typing\\_char_codes.py',
   'PYMODULE-1'),
  ('numpy._typing._dtype_like',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\numpy\\_typing\\_dtype_like.py',
   'PYMODULE-1'),
  ('numpy._typing._nbit',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\numpy\\_typing\\_nbit.py',
   'PYMODULE-1'),
  ('numpy._typing._nbit_base',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\numpy\\_typing\\_nbit_base.py',
   'PYMODULE-1'),
  ('numpy._typing._nested_sequence',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\numpy\\_typing\\_nested_sequence.py',
   'PYMODULE-1'),
  ('numpy._typing._scalars',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\numpy\\_typing\\_scalars.py',
   'PYMODULE-1'),
  ('numpy._typing._shape',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\numpy\\_typing\\_shape.py',
   'PYMODULE-1'),
  ('numpy._typing._ufunc',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\numpy\\_typing\\_ufunc.py',
   'PYMODULE-1'),
  ('numpy._utils',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\numpy\\_utils\\__init__.py',
   'PYMODULE-1'),
  ('numpy._utils._convertions',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\numpy\\_utils\\_convertions.py',
   'PYMODULE-1'),
  ('numpy._utils._inspect',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\numpy\\_utils\\_inspect.py',
   'PYMODULE-1'),
  ('numpy.char',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\numpy\\char\\__init__.py',
   'PYMODULE-1'),
  ('numpy.core',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\numpy\\core\\__init__.py',
   'PYMODULE-1'),
  ('numpy.core._utils',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\numpy\\core\\_utils.py',
   'PYMODULE-1'),
  ('numpy.ctypeslib',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\numpy\\ctypeslib\\__init__.py',
   'PYMODULE-1'),
  ('numpy.ctypeslib._ctypeslib',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\numpy\\ctypeslib\\_ctypeslib.py',
   'PYMODULE-1'),
  ('numpy.dtypes',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\numpy\\dtypes.py',
   'PYMODULE-1'),
  ('numpy.exceptions',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\numpy\\exceptions.py',
   'PYMODULE-1'),
  ('numpy.f2py',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\numpy\\f2py\\__init__.py',
   'PYMODULE-1'),
  ('numpy.f2py.__version__',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\numpy\\f2py\\__version__.py',
   'PYMODULE-1'),
  ('numpy.f2py._backends',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\numpy\\f2py\\_backends\\__init__.py',
   'PYMODULE-1'),
  ('numpy.f2py._backends._backend',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\numpy\\f2py\\_backends\\_backend.py',
   'PYMODULE-1'),
  ('numpy.f2py._backends._distutils',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\numpy\\f2py\\_backends\\_distutils.py',
   'PYMODULE-1'),
  ('numpy.f2py._backends._meson',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\numpy\\f2py\\_backends\\_meson.py',
   'PYMODULE-1'),
  ('numpy.f2py._isocbind',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\numpy\\f2py\\_isocbind.py',
   'PYMODULE-1'),
  ('numpy.f2py.auxfuncs',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\numpy\\f2py\\auxfuncs.py',
   'PYMODULE-1'),
  ('numpy.f2py.capi_maps',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\numpy\\f2py\\capi_maps.py',
   'PYMODULE-1'),
  ('numpy.f2py.cb_rules',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\numpy\\f2py\\cb_rules.py',
   'PYMODULE-1'),
  ('numpy.f2py.cfuncs',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\numpy\\f2py\\cfuncs.py',
   'PYMODULE-1'),
  ('numpy.f2py.common_rules',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\numpy\\f2py\\common_rules.py',
   'PYMODULE-1'),
  ('numpy.f2py.crackfortran',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\numpy\\f2py\\crackfortran.py',
   'PYMODULE-1'),
  ('numpy.f2py.diagnose',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\numpy\\f2py\\diagnose.py',
   'PYMODULE-1'),
  ('numpy.f2py.f2py2e',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\numpy\\f2py\\f2py2e.py',
   'PYMODULE-1'),
  ('numpy.f2py.f90mod_rules',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\numpy\\f2py\\f90mod_rules.py',
   'PYMODULE-1'),
  ('numpy.f2py.func2subr',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\numpy\\f2py\\func2subr.py',
   'PYMODULE-1'),
  ('numpy.f2py.rules',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\numpy\\f2py\\rules.py',
   'PYMODULE-1'),
  ('numpy.f2py.symbolic',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\numpy\\f2py\\symbolic.py',
   'PYMODULE-1'),
  ('numpy.f2py.use_rules',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\numpy\\f2py\\use_rules.py',
   'PYMODULE-1'),
  ('numpy.fft',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\numpy\\fft\\__init__.py',
   'PYMODULE-1'),
  ('numpy.fft._helper',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\numpy\\fft\\_helper.py',
   'PYMODULE-1'),
  ('numpy.fft._pocketfft',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\numpy\\fft\\_pocketfft.py',
   'PYMODULE-1'),
  ('numpy.fft.helper',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\numpy\\fft\\helper.py',
   'PYMODULE-1'),
  ('numpy.lib',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\numpy\\lib\\__init__.py',
   'PYMODULE-1'),
  ('numpy.lib._array_utils_impl',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\numpy\\lib\\_array_utils_impl.py',
   'PYMODULE-1'),
  ('numpy.lib._arraypad_impl',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\numpy\\lib\\_arraypad_impl.py',
   'PYMODULE-1'),
  ('numpy.lib._arraysetops_impl',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\numpy\\lib\\_arraysetops_impl.py',
   'PYMODULE-1'),
  ('numpy.lib._arrayterator_impl',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\numpy\\lib\\_arrayterator_impl.py',
   'PYMODULE-1'),
  ('numpy.lib._datasource',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\numpy\\lib\\_datasource.py',
   'PYMODULE-1'),
  ('numpy.lib._format_impl',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\numpy\\lib\\_format_impl.py',
   'PYMODULE-1'),
  ('numpy.lib._function_base_impl',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\numpy\\lib\\_function_base_impl.py',
   'PYMODULE-1'),
  ('numpy.lib._histograms_impl',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\numpy\\lib\\_histograms_impl.py',
   'PYMODULE-1'),
  ('numpy.lib._index_tricks_impl',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\numpy\\lib\\_index_tricks_impl.py',
   'PYMODULE-1'),
  ('numpy.lib._iotools',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\numpy\\lib\\_iotools.py',
   'PYMODULE-1'),
  ('numpy.lib._nanfunctions_impl',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\numpy\\lib\\_nanfunctions_impl.py',
   'PYMODULE-1'),
  ('numpy.lib._npyio_impl',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\numpy\\lib\\_npyio_impl.py',
   'PYMODULE-1'),
  ('numpy.lib._polynomial_impl',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\numpy\\lib\\_polynomial_impl.py',
   'PYMODULE-1'),
  ('numpy.lib._scimath_impl',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\numpy\\lib\\_scimath_impl.py',
   'PYMODULE-1'),
  ('numpy.lib._shape_base_impl',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\numpy\\lib\\_shape_base_impl.py',
   'PYMODULE-1'),
  ('numpy.lib._stride_tricks_impl',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\numpy\\lib\\_stride_tricks_impl.py',
   'PYMODULE-1'),
  ('numpy.lib._twodim_base_impl',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\numpy\\lib\\_twodim_base_impl.py',
   'PYMODULE-1'),
  ('numpy.lib._type_check_impl',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\numpy\\lib\\_type_check_impl.py',
   'PYMODULE-1'),
  ('numpy.lib._ufunclike_impl',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\numpy\\lib\\_ufunclike_impl.py',
   'PYMODULE-1'),
  ('numpy.lib._utils_impl',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\numpy\\lib\\_utils_impl.py',
   'PYMODULE-1'),
  ('numpy.lib._version',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\numpy\\lib\\_version.py',
   'PYMODULE-1'),
  ('numpy.lib.array_utils',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\numpy\\lib\\array_utils.py',
   'PYMODULE-1'),
  ('numpy.lib.format',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\numpy\\lib\\format.py',
   'PYMODULE-1'),
  ('numpy.lib.introspect',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\numpy\\lib\\introspect.py',
   'PYMODULE-1'),
  ('numpy.lib.mixins',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\numpy\\lib\\mixins.py',
   'PYMODULE-1'),
  ('numpy.lib.npyio',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\numpy\\lib\\npyio.py',
   'PYMODULE-1'),
  ('numpy.lib.recfunctions',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\numpy\\lib\\recfunctions.py',
   'PYMODULE-1'),
  ('numpy.lib.scimath',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\numpy\\lib\\scimath.py',
   'PYMODULE-1'),
  ('numpy.lib.stride_tricks',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\numpy\\lib\\stride_tricks.py',
   'PYMODULE-1'),
  ('numpy.linalg',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\numpy\\linalg\\__init__.py',
   'PYMODULE-1'),
  ('numpy.linalg._linalg',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\numpy\\linalg\\_linalg.py',
   'PYMODULE-1'),
  ('numpy.linalg.linalg',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\numpy\\linalg\\linalg.py',
   'PYMODULE-1'),
  ('numpy.ma',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\numpy\\ma\\__init__.py',
   'PYMODULE-1'),
  ('numpy.ma.core',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\numpy\\ma\\core.py',
   'PYMODULE-1'),
  ('numpy.ma.extras',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\numpy\\ma\\extras.py',
   'PYMODULE-1'),
  ('numpy.ma.mrecords',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\numpy\\ma\\mrecords.py',
   'PYMODULE-1'),
  ('numpy.matlib',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\numpy\\matlib.py',
   'PYMODULE-1'),
  ('numpy.matrixlib',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\numpy\\matrixlib\\__init__.py',
   'PYMODULE-1'),
  ('numpy.matrixlib.defmatrix',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\numpy\\matrixlib\\defmatrix.py',
   'PYMODULE-1'),
  ('numpy.polynomial',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\numpy\\polynomial\\__init__.py',
   'PYMODULE-1'),
  ('numpy.polynomial._polybase',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\numpy\\polynomial\\_polybase.py',
   'PYMODULE-1'),
  ('numpy.polynomial.chebyshev',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\numpy\\polynomial\\chebyshev.py',
   'PYMODULE-1'),
  ('numpy.polynomial.hermite',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\numpy\\polynomial\\hermite.py',
   'PYMODULE-1'),
  ('numpy.polynomial.hermite_e',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\numpy\\polynomial\\hermite_e.py',
   'PYMODULE-1'),
  ('numpy.polynomial.laguerre',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\numpy\\polynomial\\laguerre.py',
   'PYMODULE-1'),
  ('numpy.polynomial.legendre',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\numpy\\polynomial\\legendre.py',
   'PYMODULE-1'),
  ('numpy.polynomial.polynomial',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\numpy\\polynomial\\polynomial.py',
   'PYMODULE-1'),
  ('numpy.polynomial.polyutils',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\numpy\\polynomial\\polyutils.py',
   'PYMODULE-1'),
  ('numpy.random',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\numpy\\random\\__init__.py',
   'PYMODULE-1'),
  ('numpy.random._pickle',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\numpy\\random\\_pickle.py',
   'PYMODULE-1'),
  ('numpy.rec',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\numpy\\rec\\__init__.py',
   'PYMODULE-1'),
  ('numpy.strings',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\numpy\\strings\\__init__.py',
   'PYMODULE-1'),
  ('numpy.testing',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\numpy\\testing\\__init__.py',
   'PYMODULE-1'),
  ('numpy.testing._private',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\numpy\\testing\\_private\\__init__.py',
   'PYMODULE-1'),
  ('numpy.testing._private.extbuild',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\numpy\\testing\\_private\\extbuild.py',
   'PYMODULE-1'),
  ('numpy.testing._private.utils',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\numpy\\testing\\_private\\utils.py',
   'PYMODULE-1'),
  ('numpy.testing.overrides',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\numpy\\testing\\overrides.py',
   'PYMODULE-1'),
  ('numpy.typing',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\numpy\\typing\\__init__.py',
   'PYMODULE-1'),
  ('numpy.version',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\numpy\\version.py',
   'PYMODULE-1'),
  ('opcode',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\opcode.py',
   'PYMODULE-1'),
  ('openpyxl',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\__init__.py',
   'PYMODULE-1'),
  ('openpyxl._constants',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\_constants.py',
   'PYMODULE-1'),
  ('openpyxl.cell',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\cell\\__init__.py',
   'PYMODULE-1'),
  ('openpyxl.cell._writer',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\cell\\_writer.py',
   'PYMODULE-1'),
  ('openpyxl.cell.cell',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\cell\\cell.py',
   'PYMODULE-1'),
  ('openpyxl.cell.read_only',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\cell\\read_only.py',
   'PYMODULE-1'),
  ('openpyxl.cell.rich_text',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\cell\\rich_text.py',
   'PYMODULE-1'),
  ('openpyxl.cell.text',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\cell\\text.py',
   'PYMODULE-1'),
  ('openpyxl.chart',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\chart\\__init__.py',
   'PYMODULE-1'),
  ('openpyxl.chart._3d',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\chart\\_3d.py',
   'PYMODULE-1'),
  ('openpyxl.chart._chart',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\chart\\_chart.py',
   'PYMODULE-1'),
  ('openpyxl.chart.area_chart',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\chart\\area_chart.py',
   'PYMODULE-1'),
  ('openpyxl.chart.axis',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\chart\\axis.py',
   'PYMODULE-1'),
  ('openpyxl.chart.bar_chart',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\chart\\bar_chart.py',
   'PYMODULE-1'),
  ('openpyxl.chart.bubble_chart',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\chart\\bubble_chart.py',
   'PYMODULE-1'),
  ('openpyxl.chart.chartspace',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\chart\\chartspace.py',
   'PYMODULE-1'),
  ('openpyxl.chart.data_source',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\chart\\data_source.py',
   'PYMODULE-1'),
  ('openpyxl.chart.descriptors',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\chart\\descriptors.py',
   'PYMODULE-1'),
  ('openpyxl.chart.error_bar',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\chart\\error_bar.py',
   'PYMODULE-1'),
  ('openpyxl.chart.label',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\chart\\label.py',
   'PYMODULE-1'),
  ('openpyxl.chart.layout',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\chart\\layout.py',
   'PYMODULE-1'),
  ('openpyxl.chart.legend',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\chart\\legend.py',
   'PYMODULE-1'),
  ('openpyxl.chart.line_chart',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\chart\\line_chart.py',
   'PYMODULE-1'),
  ('openpyxl.chart.marker',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\chart\\marker.py',
   'PYMODULE-1'),
  ('openpyxl.chart.picture',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\chart\\picture.py',
   'PYMODULE-1'),
  ('openpyxl.chart.pie_chart',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\chart\\pie_chart.py',
   'PYMODULE-1'),
  ('openpyxl.chart.pivot',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\chart\\pivot.py',
   'PYMODULE-1'),
  ('openpyxl.chart.plotarea',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\chart\\plotarea.py',
   'PYMODULE-1'),
  ('openpyxl.chart.print_settings',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\chart\\print_settings.py',
   'PYMODULE-1'),
  ('openpyxl.chart.radar_chart',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\chart\\radar_chart.py',
   'PYMODULE-1'),
  ('openpyxl.chart.reader',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\chart\\reader.py',
   'PYMODULE-1'),
  ('openpyxl.chart.reference',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\chart\\reference.py',
   'PYMODULE-1'),
  ('openpyxl.chart.scatter_chart',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\chart\\scatter_chart.py',
   'PYMODULE-1'),
  ('openpyxl.chart.series',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\chart\\series.py',
   'PYMODULE-1'),
  ('openpyxl.chart.series_factory',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\chart\\series_factory.py',
   'PYMODULE-1'),
  ('openpyxl.chart.shapes',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\chart\\shapes.py',
   'PYMODULE-1'),
  ('openpyxl.chart.stock_chart',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\chart\\stock_chart.py',
   'PYMODULE-1'),
  ('openpyxl.chart.surface_chart',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\chart\\surface_chart.py',
   'PYMODULE-1'),
  ('openpyxl.chart.text',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\chart\\text.py',
   'PYMODULE-1'),
  ('openpyxl.chart.title',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\chart\\title.py',
   'PYMODULE-1'),
  ('openpyxl.chart.trendline',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\chart\\trendline.py',
   'PYMODULE-1'),
  ('openpyxl.chart.updown_bars',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\chart\\updown_bars.py',
   'PYMODULE-1'),
  ('openpyxl.chartsheet',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\chartsheet\\__init__.py',
   'PYMODULE-1'),
  ('openpyxl.chartsheet.chartsheet',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\chartsheet\\chartsheet.py',
   'PYMODULE-1'),
  ('openpyxl.chartsheet.custom',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\chartsheet\\custom.py',
   'PYMODULE-1'),
  ('openpyxl.chartsheet.properties',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\chartsheet\\properties.py',
   'PYMODULE-1'),
  ('openpyxl.chartsheet.protection',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\chartsheet\\protection.py',
   'PYMODULE-1'),
  ('openpyxl.chartsheet.publish',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\chartsheet\\publish.py',
   'PYMODULE-1'),
  ('openpyxl.chartsheet.relation',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\chartsheet\\relation.py',
   'PYMODULE-1'),
  ('openpyxl.chartsheet.views',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\chartsheet\\views.py',
   'PYMODULE-1'),
  ('openpyxl.comments',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\comments\\__init__.py',
   'PYMODULE-1'),
  ('openpyxl.comments.author',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\comments\\author.py',
   'PYMODULE-1'),
  ('openpyxl.comments.comment_sheet',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\comments\\comment_sheet.py',
   'PYMODULE-1'),
  ('openpyxl.comments.comments',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\comments\\comments.py',
   'PYMODULE-1'),
  ('openpyxl.comments.shape_writer',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\comments\\shape_writer.py',
   'PYMODULE-1'),
  ('openpyxl.compat',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\compat\\__init__.py',
   'PYMODULE-1'),
  ('openpyxl.compat.numbers',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\compat\\numbers.py',
   'PYMODULE-1'),
  ('openpyxl.compat.product',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\compat\\product.py',
   'PYMODULE-1'),
  ('openpyxl.compat.strings',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\compat\\strings.py',
   'PYMODULE-1'),
  ('openpyxl.descriptors',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\descriptors\\__init__.py',
   'PYMODULE-1'),
  ('openpyxl.descriptors.base',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\descriptors\\base.py',
   'PYMODULE-1'),
  ('openpyxl.descriptors.container',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\descriptors\\container.py',
   'PYMODULE-1'),
  ('openpyxl.descriptors.excel',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\descriptors\\excel.py',
   'PYMODULE-1'),
  ('openpyxl.descriptors.namespace',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\descriptors\\namespace.py',
   'PYMODULE-1'),
  ('openpyxl.descriptors.nested',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\descriptors\\nested.py',
   'PYMODULE-1'),
  ('openpyxl.descriptors.sequence',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\descriptors\\sequence.py',
   'PYMODULE-1'),
  ('openpyxl.descriptors.serialisable',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\descriptors\\serialisable.py',
   'PYMODULE-1'),
  ('openpyxl.drawing',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\drawing\\__init__.py',
   'PYMODULE-1'),
  ('openpyxl.drawing.colors',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\drawing\\colors.py',
   'PYMODULE-1'),
  ('openpyxl.drawing.connector',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\drawing\\connector.py',
   'PYMODULE-1'),
  ('openpyxl.drawing.drawing',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\drawing\\drawing.py',
   'PYMODULE-1'),
  ('openpyxl.drawing.effect',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\drawing\\effect.py',
   'PYMODULE-1'),
  ('openpyxl.drawing.fill',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\drawing\\fill.py',
   'PYMODULE-1'),
  ('openpyxl.drawing.geometry',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\drawing\\geometry.py',
   'PYMODULE-1'),
  ('openpyxl.drawing.graphic',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\drawing\\graphic.py',
   'PYMODULE-1'),
  ('openpyxl.drawing.image',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\drawing\\image.py',
   'PYMODULE-1'),
  ('openpyxl.drawing.line',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\drawing\\line.py',
   'PYMODULE-1'),
  ('openpyxl.drawing.picture',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\drawing\\picture.py',
   'PYMODULE-1'),
  ('openpyxl.drawing.properties',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\drawing\\properties.py',
   'PYMODULE-1'),
  ('openpyxl.drawing.relation',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\drawing\\relation.py',
   'PYMODULE-1'),
  ('openpyxl.drawing.spreadsheet_drawing',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\drawing\\spreadsheet_drawing.py',
   'PYMODULE-1'),
  ('openpyxl.drawing.text',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\drawing\\text.py',
   'PYMODULE-1'),
  ('openpyxl.drawing.xdr',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\drawing\\xdr.py',
   'PYMODULE-1'),
  ('openpyxl.formatting',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\formatting\\__init__.py',
   'PYMODULE-1'),
  ('openpyxl.formatting.formatting',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\formatting\\formatting.py',
   'PYMODULE-1'),
  ('openpyxl.formatting.rule',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\formatting\\rule.py',
   'PYMODULE-1'),
  ('openpyxl.formula',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\formula\\__init__.py',
   'PYMODULE-1'),
  ('openpyxl.formula.tokenizer',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\formula\\tokenizer.py',
   'PYMODULE-1'),
  ('openpyxl.formula.translate',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\formula\\translate.py',
   'PYMODULE-1'),
  ('openpyxl.packaging',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\packaging\\__init__.py',
   'PYMODULE-1'),
  ('openpyxl.packaging.core',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\packaging\\core.py',
   'PYMODULE-1'),
  ('openpyxl.packaging.custom',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\packaging\\custom.py',
   'PYMODULE-1'),
  ('openpyxl.packaging.extended',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\packaging\\extended.py',
   'PYMODULE-1'),
  ('openpyxl.packaging.manifest',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\packaging\\manifest.py',
   'PYMODULE-1'),
  ('openpyxl.packaging.relationship',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\packaging\\relationship.py',
   'PYMODULE-1'),
  ('openpyxl.packaging.workbook',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\packaging\\workbook.py',
   'PYMODULE-1'),
  ('openpyxl.pivot',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\pivot\\__init__.py',
   'PYMODULE-1'),
  ('openpyxl.pivot.cache',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\pivot\\cache.py',
   'PYMODULE-1'),
  ('openpyxl.pivot.fields',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\pivot\\fields.py',
   'PYMODULE-1'),
  ('openpyxl.pivot.record',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\pivot\\record.py',
   'PYMODULE-1'),
  ('openpyxl.pivot.table',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\pivot\\table.py',
   'PYMODULE-1'),
  ('openpyxl.reader',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\reader\\__init__.py',
   'PYMODULE-1'),
  ('openpyxl.reader.drawings',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\reader\\drawings.py',
   'PYMODULE-1'),
  ('openpyxl.reader.excel',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\reader\\excel.py',
   'PYMODULE-1'),
  ('openpyxl.reader.strings',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\reader\\strings.py',
   'PYMODULE-1'),
  ('openpyxl.reader.workbook',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\reader\\workbook.py',
   'PYMODULE-1'),
  ('openpyxl.styles',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\styles\\__init__.py',
   'PYMODULE-1'),
  ('openpyxl.styles.alignment',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\styles\\alignment.py',
   'PYMODULE-1'),
  ('openpyxl.styles.borders',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\styles\\borders.py',
   'PYMODULE-1'),
  ('openpyxl.styles.builtins',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\styles\\builtins.py',
   'PYMODULE-1'),
  ('openpyxl.styles.cell_style',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\styles\\cell_style.py',
   'PYMODULE-1'),
  ('openpyxl.styles.colors',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\styles\\colors.py',
   'PYMODULE-1'),
  ('openpyxl.styles.differential',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\styles\\differential.py',
   'PYMODULE-1'),
  ('openpyxl.styles.fills',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\styles\\fills.py',
   'PYMODULE-1'),
  ('openpyxl.styles.fonts',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\styles\\fonts.py',
   'PYMODULE-1'),
  ('openpyxl.styles.named_styles',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\styles\\named_styles.py',
   'PYMODULE-1'),
  ('openpyxl.styles.numbers',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\styles\\numbers.py',
   'PYMODULE-1'),
  ('openpyxl.styles.protection',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\styles\\protection.py',
   'PYMODULE-1'),
  ('openpyxl.styles.proxy',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\styles\\proxy.py',
   'PYMODULE-1'),
  ('openpyxl.styles.styleable',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\styles\\styleable.py',
   'PYMODULE-1'),
  ('openpyxl.styles.stylesheet',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\styles\\stylesheet.py',
   'PYMODULE-1'),
  ('openpyxl.styles.table',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\styles\\table.py',
   'PYMODULE-1'),
  ('openpyxl.utils',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\utils\\__init__.py',
   'PYMODULE-1'),
  ('openpyxl.utils.bound_dictionary',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\utils\\bound_dictionary.py',
   'PYMODULE-1'),
  ('openpyxl.utils.cell',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\utils\\cell.py',
   'PYMODULE-1'),
  ('openpyxl.utils.dataframe',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\utils\\dataframe.py',
   'PYMODULE-1'),
  ('openpyxl.utils.datetime',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\utils\\datetime.py',
   'PYMODULE-1'),
  ('openpyxl.utils.escape',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\utils\\escape.py',
   'PYMODULE-1'),
  ('openpyxl.utils.exceptions',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\utils\\exceptions.py',
   'PYMODULE-1'),
  ('openpyxl.utils.formulas',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\utils\\formulas.py',
   'PYMODULE-1'),
  ('openpyxl.utils.indexed_list',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\utils\\indexed_list.py',
   'PYMODULE-1'),
  ('openpyxl.utils.protection',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\utils\\protection.py',
   'PYMODULE-1'),
  ('openpyxl.utils.units',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\utils\\units.py',
   'PYMODULE-1'),
  ('openpyxl.workbook',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\workbook\\__init__.py',
   'PYMODULE-1'),
  ('openpyxl.workbook._writer',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\workbook\\_writer.py',
   'PYMODULE-1'),
  ('openpyxl.workbook.child',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\workbook\\child.py',
   'PYMODULE-1'),
  ('openpyxl.workbook.defined_name',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\workbook\\defined_name.py',
   'PYMODULE-1'),
  ('openpyxl.workbook.external_link',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\workbook\\external_link\\__init__.py',
   'PYMODULE-1'),
  ('openpyxl.workbook.external_link.external',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\workbook\\external_link\\external.py',
   'PYMODULE-1'),
  ('openpyxl.workbook.external_reference',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\workbook\\external_reference.py',
   'PYMODULE-1'),
  ('openpyxl.workbook.function_group',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\workbook\\function_group.py',
   'PYMODULE-1'),
  ('openpyxl.workbook.properties',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\workbook\\properties.py',
   'PYMODULE-1'),
  ('openpyxl.workbook.protection',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\workbook\\protection.py',
   'PYMODULE-1'),
  ('openpyxl.workbook.smart_tags',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\workbook\\smart_tags.py',
   'PYMODULE-1'),
  ('openpyxl.workbook.views',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\workbook\\views.py',
   'PYMODULE-1'),
  ('openpyxl.workbook.web',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\workbook\\web.py',
   'PYMODULE-1'),
  ('openpyxl.workbook.workbook',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\workbook\\workbook.py',
   'PYMODULE-1'),
  ('openpyxl.worksheet',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\__init__.py',
   'PYMODULE-1'),
  ('openpyxl.worksheet._read_only',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\_read_only.py',
   'PYMODULE-1'),
  ('openpyxl.worksheet._reader',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\_reader.py',
   'PYMODULE-1'),
  ('openpyxl.worksheet._write_only',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\_write_only.py',
   'PYMODULE-1'),
  ('openpyxl.worksheet._writer',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\_writer.py',
   'PYMODULE-1'),
  ('openpyxl.worksheet.cell_range',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\cell_range.py',
   'PYMODULE-1'),
  ('openpyxl.worksheet.copier',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\copier.py',
   'PYMODULE-1'),
  ('openpyxl.worksheet.datavalidation',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\datavalidation.py',
   'PYMODULE-1'),
  ('openpyxl.worksheet.dimensions',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\dimensions.py',
   'PYMODULE-1'),
  ('openpyxl.worksheet.drawing',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\drawing.py',
   'PYMODULE-1'),
  ('openpyxl.worksheet.filters',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\filters.py',
   'PYMODULE-1'),
  ('openpyxl.worksheet.formula',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\formula.py',
   'PYMODULE-1'),
  ('openpyxl.worksheet.header_footer',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\header_footer.py',
   'PYMODULE-1'),
  ('openpyxl.worksheet.hyperlink',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\hyperlink.py',
   'PYMODULE-1'),
  ('openpyxl.worksheet.merge',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\merge.py',
   'PYMODULE-1'),
  ('openpyxl.worksheet.page',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\page.py',
   'PYMODULE-1'),
  ('openpyxl.worksheet.pagebreak',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\pagebreak.py',
   'PYMODULE-1'),
  ('openpyxl.worksheet.print_settings',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\print_settings.py',
   'PYMODULE-1'),
  ('openpyxl.worksheet.properties',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\properties.py',
   'PYMODULE-1'),
  ('openpyxl.worksheet.protection',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\protection.py',
   'PYMODULE-1'),
  ('openpyxl.worksheet.related',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\related.py',
   'PYMODULE-1'),
  ('openpyxl.worksheet.scenario',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\scenario.py',
   'PYMODULE-1'),
  ('openpyxl.worksheet.table',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\table.py',
   'PYMODULE-1'),
  ('openpyxl.worksheet.views',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\views.py',
   'PYMODULE-1'),
  ('openpyxl.worksheet.worksheet',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\worksheet.py',
   'PYMODULE-1'),
  ('openpyxl.writer',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\writer\\__init__.py',
   'PYMODULE-1'),
  ('openpyxl.writer.excel',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\writer\\excel.py',
   'PYMODULE-1'),
  ('openpyxl.writer.theme',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\writer\\theme.py',
   'PYMODULE-1'),
  ('openpyxl.xml',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\xml\\__init__.py',
   'PYMODULE-1'),
  ('openpyxl.xml.constants',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\xml\\constants.py',
   'PYMODULE-1'),
  ('openpyxl.xml.functions',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\openpyxl\\xml\\functions.py',
   'PYMODULE-1'),
  ('packaging',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\packaging\\__init__.py',
   'PYMODULE-1'),
  ('packaging._structures',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\packaging\\_structures.py',
   'PYMODULE-1'),
  ('packaging.version',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\packaging\\version.py',
   'PYMODULE-1'),
  ('pandas',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\__init__.py',
   'PYMODULE-1'),
  ('pandas._config',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\_config\\__init__.py',
   'PYMODULE-1'),
  ('pandas._config.config',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\_config\\config.py',
   'PYMODULE-1'),
  ('pandas._config.dates',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\_config\\dates.py',
   'PYMODULE-1'),
  ('pandas._config.display',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\_config\\display.py',
   'PYMODULE-1'),
  ('pandas._config.localization',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\_config\\localization.py',
   'PYMODULE-1'),
  ('pandas._libs',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\_libs\\__init__.py',
   'PYMODULE-1'),
  ('pandas._libs.tslibs',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\_libs\\tslibs\\__init__.py',
   'PYMODULE-1'),
  ('pandas._libs.window',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\_libs\\window\\__init__.py',
   'PYMODULE-1'),
  ('pandas._testing',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\_testing\\__init__.py',
   'PYMODULE-1'),
  ('pandas._testing._io',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\_testing\\_io.py',
   'PYMODULE-1'),
  ('pandas._testing._warnings',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\_testing\\_warnings.py',
   'PYMODULE-1'),
  ('pandas._testing.asserters',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\_testing\\asserters.py',
   'PYMODULE-1'),
  ('pandas._testing.compat',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\_testing\\compat.py',
   'PYMODULE-1'),
  ('pandas._testing.contexts',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\_testing\\contexts.py',
   'PYMODULE-1'),
  ('pandas._typing',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\_typing.py',
   'PYMODULE-1'),
  ('pandas._version',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\_version.py',
   'PYMODULE-1'),
  ('pandas._version_meson',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\_version_meson.py',
   'PYMODULE-1'),
  ('pandas.api',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\api\\__init__.py',
   'PYMODULE-1'),
  ('pandas.api.extensions',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\api\\extensions\\__init__.py',
   'PYMODULE-1'),
  ('pandas.api.indexers',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\api\\indexers\\__init__.py',
   'PYMODULE-1'),
  ('pandas.api.interchange',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\api\\interchange\\__init__.py',
   'PYMODULE-1'),
  ('pandas.api.types',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\api\\types\\__init__.py',
   'PYMODULE-1'),
  ('pandas.api.typing',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\api\\typing\\__init__.py',
   'PYMODULE-1'),
  ('pandas.arrays',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\arrays\\__init__.py',
   'PYMODULE-1'),
  ('pandas.compat',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\compat\\__init__.py',
   'PYMODULE-1'),
  ('pandas.compat._constants',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\compat\\_constants.py',
   'PYMODULE-1'),
  ('pandas.compat._optional',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\compat\\_optional.py',
   'PYMODULE-1'),
  ('pandas.compat.compressors',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\compat\\compressors.py',
   'PYMODULE-1'),
  ('pandas.compat.numpy',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\compat\\numpy\\__init__.py',
   'PYMODULE-1'),
  ('pandas.compat.numpy.function',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\compat\\numpy\\function.py',
   'PYMODULE-1'),
  ('pandas.compat.pickle_compat',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\compat\\pickle_compat.py',
   'PYMODULE-1'),
  ('pandas.compat.pyarrow',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\compat\\pyarrow.py',
   'PYMODULE-1'),
  ('pandas.core',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\__init__.py',
   'PYMODULE-1'),
  ('pandas.core._numba',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\_numba\\__init__.py',
   'PYMODULE-1'),
  ('pandas.core._numba.executor',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\_numba\\executor.py',
   'PYMODULE-1'),
  ('pandas.core._numba.extensions',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\_numba\\extensions.py',
   'PYMODULE-1'),
  ('pandas.core._numba.kernels',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\__init__.py',
   'PYMODULE-1'),
  ('pandas.core._numba.kernels.mean_',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\mean_.py',
   'PYMODULE-1'),
  ('pandas.core._numba.kernels.min_max_',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\min_max_.py',
   'PYMODULE-1'),
  ('pandas.core._numba.kernels.shared',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\shared.py',
   'PYMODULE-1'),
  ('pandas.core._numba.kernels.sum_',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\sum_.py',
   'PYMODULE-1'),
  ('pandas.core._numba.kernels.var_',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\var_.py',
   'PYMODULE-1'),
  ('pandas.core.accessor',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\accessor.py',
   'PYMODULE-1'),
  ('pandas.core.algorithms',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\algorithms.py',
   'PYMODULE-1'),
  ('pandas.core.api',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\api.py',
   'PYMODULE-1'),
  ('pandas.core.apply',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\apply.py',
   'PYMODULE-1'),
  ('pandas.core.array_algos',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\array_algos\\__init__.py',
   'PYMODULE-1'),
  ('pandas.core.array_algos.datetimelike_accumulations',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\array_algos\\datetimelike_accumulations.py',
   'PYMODULE-1'),
  ('pandas.core.array_algos.masked_accumulations',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\array_algos\\masked_accumulations.py',
   'PYMODULE-1'),
  ('pandas.core.array_algos.masked_reductions',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\array_algos\\masked_reductions.py',
   'PYMODULE-1'),
  ('pandas.core.array_algos.putmask',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\array_algos\\putmask.py',
   'PYMODULE-1'),
  ('pandas.core.array_algos.quantile',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\array_algos\\quantile.py',
   'PYMODULE-1'),
  ('pandas.core.array_algos.replace',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\array_algos\\replace.py',
   'PYMODULE-1'),
  ('pandas.core.array_algos.take',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\array_algos\\take.py',
   'PYMODULE-1'),
  ('pandas.core.array_algos.transforms',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\array_algos\\transforms.py',
   'PYMODULE-1'),
  ('pandas.core.arraylike',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\arraylike.py',
   'PYMODULE-1'),
  ('pandas.core.arrays',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\__init__.py',
   'PYMODULE-1'),
  ('pandas.core.arrays._arrow_string_mixins',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\_arrow_string_mixins.py',
   'PYMODULE-1'),
  ('pandas.core.arrays._mixins',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\_mixins.py',
   'PYMODULE-1'),
  ('pandas.core.arrays._ranges',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\_ranges.py',
   'PYMODULE-1'),
  ('pandas.core.arrays._utils',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\_utils.py',
   'PYMODULE-1'),
  ('pandas.core.arrays.arrow',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\__init__.py',
   'PYMODULE-1'),
  ('pandas.core.arrays.arrow._arrow_utils',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\_arrow_utils.py',
   'PYMODULE-1'),
  ('pandas.core.arrays.arrow.accessors',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\accessors.py',
   'PYMODULE-1'),
  ('pandas.core.arrays.arrow.array',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\array.py',
   'PYMODULE-1'),
  ('pandas.core.arrays.arrow.extension_types',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\extension_types.py',
   'PYMODULE-1'),
  ('pandas.core.arrays.base',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\base.py',
   'PYMODULE-1'),
  ('pandas.core.arrays.boolean',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\boolean.py',
   'PYMODULE-1'),
  ('pandas.core.arrays.categorical',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\categorical.py',
   'PYMODULE-1'),
  ('pandas.core.arrays.datetimelike',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\datetimelike.py',
   'PYMODULE-1'),
  ('pandas.core.arrays.datetimes',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\datetimes.py',
   'PYMODULE-1'),
  ('pandas.core.arrays.floating',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\floating.py',
   'PYMODULE-1'),
  ('pandas.core.arrays.integer',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\integer.py',
   'PYMODULE-1'),
  ('pandas.core.arrays.interval',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\interval.py',
   'PYMODULE-1'),
  ('pandas.core.arrays.masked',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\masked.py',
   'PYMODULE-1'),
  ('pandas.core.arrays.numeric',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\numeric.py',
   'PYMODULE-1'),
  ('pandas.core.arrays.numpy_',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\numpy_.py',
   'PYMODULE-1'),
  ('pandas.core.arrays.period',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\period.py',
   'PYMODULE-1'),
  ('pandas.core.arrays.sparse',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\sparse\\__init__.py',
   'PYMODULE-1'),
  ('pandas.core.arrays.sparse.accessor',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\sparse\\accessor.py',
   'PYMODULE-1'),
  ('pandas.core.arrays.sparse.array',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\sparse\\array.py',
   'PYMODULE-1'),
  ('pandas.core.arrays.sparse.scipy_sparse',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\sparse\\scipy_sparse.py',
   'PYMODULE-1'),
  ('pandas.core.arrays.string_',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\string_.py',
   'PYMODULE-1'),
  ('pandas.core.arrays.string_arrow',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\string_arrow.py',
   'PYMODULE-1'),
  ('pandas.core.arrays.timedeltas',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\timedeltas.py',
   'PYMODULE-1'),
  ('pandas.core.base',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\base.py',
   'PYMODULE-1'),
  ('pandas.core.common',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\common.py',
   'PYMODULE-1'),
  ('pandas.core.computation',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\computation\\__init__.py',
   'PYMODULE-1'),
  ('pandas.core.computation.align',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\computation\\align.py',
   'PYMODULE-1'),
  ('pandas.core.computation.api',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\computation\\api.py',
   'PYMODULE-1'),
  ('pandas.core.computation.check',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\computation\\check.py',
   'PYMODULE-1'),
  ('pandas.core.computation.common',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\computation\\common.py',
   'PYMODULE-1'),
  ('pandas.core.computation.engines',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\computation\\engines.py',
   'PYMODULE-1'),
  ('pandas.core.computation.eval',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\computation\\eval.py',
   'PYMODULE-1'),
  ('pandas.core.computation.expr',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\computation\\expr.py',
   'PYMODULE-1'),
  ('pandas.core.computation.expressions',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\computation\\expressions.py',
   'PYMODULE-1'),
  ('pandas.core.computation.ops',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\computation\\ops.py',
   'PYMODULE-1'),
  ('pandas.core.computation.parsing',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\computation\\parsing.py',
   'PYMODULE-1'),
  ('pandas.core.computation.pytables',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\computation\\pytables.py',
   'PYMODULE-1'),
  ('pandas.core.computation.scope',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\computation\\scope.py',
   'PYMODULE-1'),
  ('pandas.core.config_init',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\config_init.py',
   'PYMODULE-1'),
  ('pandas.core.construction',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\construction.py',
   'PYMODULE-1'),
  ('pandas.core.dtypes',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\dtypes\\__init__.py',
   'PYMODULE-1'),
  ('pandas.core.dtypes.api',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\dtypes\\api.py',
   'PYMODULE-1'),
  ('pandas.core.dtypes.astype',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\dtypes\\astype.py',
   'PYMODULE-1'),
  ('pandas.core.dtypes.base',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\dtypes\\base.py',
   'PYMODULE-1'),
  ('pandas.core.dtypes.cast',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\dtypes\\cast.py',
   'PYMODULE-1'),
  ('pandas.core.dtypes.common',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\dtypes\\common.py',
   'PYMODULE-1'),
  ('pandas.core.dtypes.concat',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\dtypes\\concat.py',
   'PYMODULE-1'),
  ('pandas.core.dtypes.dtypes',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\dtypes\\dtypes.py',
   'PYMODULE-1'),
  ('pandas.core.dtypes.generic',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\dtypes\\generic.py',
   'PYMODULE-1'),
  ('pandas.core.dtypes.inference',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\dtypes\\inference.py',
   'PYMODULE-1'),
  ('pandas.core.dtypes.missing',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\dtypes\\missing.py',
   'PYMODULE-1'),
  ('pandas.core.flags',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\flags.py',
   'PYMODULE-1'),
  ('pandas.core.frame',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\frame.py',
   'PYMODULE-1'),
  ('pandas.core.generic',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\generic.py',
   'PYMODULE-1'),
  ('pandas.core.groupby',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\groupby\\__init__.py',
   'PYMODULE-1'),
  ('pandas.core.groupby.base',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\groupby\\base.py',
   'PYMODULE-1'),
  ('pandas.core.groupby.categorical',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\groupby\\categorical.py',
   'PYMODULE-1'),
  ('pandas.core.groupby.generic',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\groupby\\generic.py',
   'PYMODULE-1'),
  ('pandas.core.groupby.groupby',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\groupby\\groupby.py',
   'PYMODULE-1'),
  ('pandas.core.groupby.grouper',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\groupby\\grouper.py',
   'PYMODULE-1'),
  ('pandas.core.groupby.indexing',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\groupby\\indexing.py',
   'PYMODULE-1'),
  ('pandas.core.groupby.numba_',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\groupby\\numba_.py',
   'PYMODULE-1'),
  ('pandas.core.groupby.ops',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\groupby\\ops.py',
   'PYMODULE-1'),
  ('pandas.core.indexers',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\indexers\\__init__.py',
   'PYMODULE-1'),
  ('pandas.core.indexers.objects',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\indexers\\objects.py',
   'PYMODULE-1'),
  ('pandas.core.indexers.utils',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\indexers\\utils.py',
   'PYMODULE-1'),
  ('pandas.core.indexes',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\indexes\\__init__.py',
   'PYMODULE-1'),
  ('pandas.core.indexes.accessors',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\indexes\\accessors.py',
   'PYMODULE-1'),
  ('pandas.core.indexes.api',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\indexes\\api.py',
   'PYMODULE-1'),
  ('pandas.core.indexes.base',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\indexes\\base.py',
   'PYMODULE-1'),
  ('pandas.core.indexes.category',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\indexes\\category.py',
   'PYMODULE-1'),
  ('pandas.core.indexes.datetimelike',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\indexes\\datetimelike.py',
   'PYMODULE-1'),
  ('pandas.core.indexes.datetimes',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\indexes\\datetimes.py',
   'PYMODULE-1'),
  ('pandas.core.indexes.extension',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\indexes\\extension.py',
   'PYMODULE-1'),
  ('pandas.core.indexes.frozen',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\indexes\\frozen.py',
   'PYMODULE-1'),
  ('pandas.core.indexes.interval',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\indexes\\interval.py',
   'PYMODULE-1'),
  ('pandas.core.indexes.multi',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\indexes\\multi.py',
   'PYMODULE-1'),
  ('pandas.core.indexes.period',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\indexes\\period.py',
   'PYMODULE-1'),
  ('pandas.core.indexes.range',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\indexes\\range.py',
   'PYMODULE-1'),
  ('pandas.core.indexes.timedeltas',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\indexes\\timedeltas.py',
   'PYMODULE-1'),
  ('pandas.core.indexing',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\indexing.py',
   'PYMODULE-1'),
  ('pandas.core.interchange',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\interchange\\__init__.py',
   'PYMODULE-1'),
  ('pandas.core.interchange.buffer',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\interchange\\buffer.py',
   'PYMODULE-1'),
  ('pandas.core.interchange.column',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\interchange\\column.py',
   'PYMODULE-1'),
  ('pandas.core.interchange.dataframe',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\interchange\\dataframe.py',
   'PYMODULE-1'),
  ('pandas.core.interchange.dataframe_protocol',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\interchange\\dataframe_protocol.py',
   'PYMODULE-1'),
  ('pandas.core.interchange.from_dataframe',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\interchange\\from_dataframe.py',
   'PYMODULE-1'),
  ('pandas.core.interchange.utils',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\interchange\\utils.py',
   'PYMODULE-1'),
  ('pandas.core.internals',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\internals\\__init__.py',
   'PYMODULE-1'),
  ('pandas.core.internals.api',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\internals\\api.py',
   'PYMODULE-1'),
  ('pandas.core.internals.array_manager',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\internals\\array_manager.py',
   'PYMODULE-1'),
  ('pandas.core.internals.base',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\internals\\base.py',
   'PYMODULE-1'),
  ('pandas.core.internals.blocks',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\internals\\blocks.py',
   'PYMODULE-1'),
  ('pandas.core.internals.concat',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\internals\\concat.py',
   'PYMODULE-1'),
  ('pandas.core.internals.construction',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\internals\\construction.py',
   'PYMODULE-1'),
  ('pandas.core.internals.managers',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\internals\\managers.py',
   'PYMODULE-1'),
  ('pandas.core.internals.ops',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\internals\\ops.py',
   'PYMODULE-1'),
  ('pandas.core.methods',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\methods\\__init__.py',
   'PYMODULE-1'),
  ('pandas.core.methods.describe',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\methods\\describe.py',
   'PYMODULE-1'),
  ('pandas.core.methods.selectn',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\methods\\selectn.py',
   'PYMODULE-1'),
  ('pandas.core.methods.to_dict',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\methods\\to_dict.py',
   'PYMODULE-1'),
  ('pandas.core.missing',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\missing.py',
   'PYMODULE-1'),
  ('pandas.core.nanops',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\nanops.py',
   'PYMODULE-1'),
  ('pandas.core.ops',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\ops\\__init__.py',
   'PYMODULE-1'),
  ('pandas.core.ops.array_ops',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\ops\\array_ops.py',
   'PYMODULE-1'),
  ('pandas.core.ops.common',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\ops\\common.py',
   'PYMODULE-1'),
  ('pandas.core.ops.dispatch',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\ops\\dispatch.py',
   'PYMODULE-1'),
  ('pandas.core.ops.docstrings',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\ops\\docstrings.py',
   'PYMODULE-1'),
  ('pandas.core.ops.invalid',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\ops\\invalid.py',
   'PYMODULE-1'),
  ('pandas.core.ops.mask_ops',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\ops\\mask_ops.py',
   'PYMODULE-1'),
  ('pandas.core.ops.missing',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\ops\\missing.py',
   'PYMODULE-1'),
  ('pandas.core.resample',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\resample.py',
   'PYMODULE-1'),
  ('pandas.core.reshape',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\reshape\\__init__.py',
   'PYMODULE-1'),
  ('pandas.core.reshape.api',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\reshape\\api.py',
   'PYMODULE-1'),
  ('pandas.core.reshape.concat',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\reshape\\concat.py',
   'PYMODULE-1'),
  ('pandas.core.reshape.encoding',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\reshape\\encoding.py',
   'PYMODULE-1'),
  ('pandas.core.reshape.melt',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\reshape\\melt.py',
   'PYMODULE-1'),
  ('pandas.core.reshape.merge',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\reshape\\merge.py',
   'PYMODULE-1'),
  ('pandas.core.reshape.pivot',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\reshape\\pivot.py',
   'PYMODULE-1'),
  ('pandas.core.reshape.reshape',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\reshape\\reshape.py',
   'PYMODULE-1'),
  ('pandas.core.reshape.tile',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\reshape\\tile.py',
   'PYMODULE-1'),
  ('pandas.core.reshape.util',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\reshape\\util.py',
   'PYMODULE-1'),
  ('pandas.core.roperator',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\roperator.py',
   'PYMODULE-1'),
  ('pandas.core.sample',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\sample.py',
   'PYMODULE-1'),
  ('pandas.core.series',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\series.py',
   'PYMODULE-1'),
  ('pandas.core.shared_docs',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\shared_docs.py',
   'PYMODULE-1'),
  ('pandas.core.sorting',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\sorting.py',
   'PYMODULE-1'),
  ('pandas.core.strings',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\strings\\__init__.py',
   'PYMODULE-1'),
  ('pandas.core.strings.accessor',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\strings\\accessor.py',
   'PYMODULE-1'),
  ('pandas.core.strings.base',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\strings\\base.py',
   'PYMODULE-1'),
  ('pandas.core.strings.object_array',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\strings\\object_array.py',
   'PYMODULE-1'),
  ('pandas.core.tools',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\tools\\__init__.py',
   'PYMODULE-1'),
  ('pandas.core.tools.datetimes',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\tools\\datetimes.py',
   'PYMODULE-1'),
  ('pandas.core.tools.numeric',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\tools\\numeric.py',
   'PYMODULE-1'),
  ('pandas.core.tools.timedeltas',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\tools\\timedeltas.py',
   'PYMODULE-1'),
  ('pandas.core.tools.times',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\tools\\times.py',
   'PYMODULE-1'),
  ('pandas.core.util',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\util\\__init__.py',
   'PYMODULE-1'),
  ('pandas.core.util.hashing',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\util\\hashing.py',
   'PYMODULE-1'),
  ('pandas.core.util.numba_',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\util\\numba_.py',
   'PYMODULE-1'),
  ('pandas.core.window',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\window\\__init__.py',
   'PYMODULE-1'),
  ('pandas.core.window.common',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\window\\common.py',
   'PYMODULE-1'),
  ('pandas.core.window.doc',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\window\\doc.py',
   'PYMODULE-1'),
  ('pandas.core.window.ewm',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\window\\ewm.py',
   'PYMODULE-1'),
  ('pandas.core.window.expanding',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\window\\expanding.py',
   'PYMODULE-1'),
  ('pandas.core.window.numba_',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\window\\numba_.py',
   'PYMODULE-1'),
  ('pandas.core.window.online',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\window\\online.py',
   'PYMODULE-1'),
  ('pandas.core.window.rolling',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\core\\window\\rolling.py',
   'PYMODULE-1'),
  ('pandas.errors',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\errors\\__init__.py',
   'PYMODULE-1'),
  ('pandas.io',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\io\\__init__.py',
   'PYMODULE-1'),
  ('pandas.io._util',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\io\\_util.py',
   'PYMODULE-1'),
  ('pandas.io.api',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\io\\api.py',
   'PYMODULE-1'),
  ('pandas.io.clipboard',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\io\\clipboard\\__init__.py',
   'PYMODULE-1'),
  ('pandas.io.clipboards',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\io\\clipboards.py',
   'PYMODULE-1'),
  ('pandas.io.common',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\io\\common.py',
   'PYMODULE-1'),
  ('pandas.io.excel',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\io\\excel\\__init__.py',
   'PYMODULE-1'),
  ('pandas.io.excel._base',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\io\\excel\\_base.py',
   'PYMODULE-1'),
  ('pandas.io.excel._calamine',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\io\\excel\\_calamine.py',
   'PYMODULE-1'),
  ('pandas.io.excel._odfreader',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\io\\excel\\_odfreader.py',
   'PYMODULE-1'),
  ('pandas.io.excel._odswriter',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\io\\excel\\_odswriter.py',
   'PYMODULE-1'),
  ('pandas.io.excel._openpyxl',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\io\\excel\\_openpyxl.py',
   'PYMODULE-1'),
  ('pandas.io.excel._pyxlsb',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\io\\excel\\_pyxlsb.py',
   'PYMODULE-1'),
  ('pandas.io.excel._util',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\io\\excel\\_util.py',
   'PYMODULE-1'),
  ('pandas.io.excel._xlrd',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\io\\excel\\_xlrd.py',
   'PYMODULE-1'),
  ('pandas.io.excel._xlsxwriter',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\io\\excel\\_xlsxwriter.py',
   'PYMODULE-1'),
  ('pandas.io.feather_format',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\io\\feather_format.py',
   'PYMODULE-1'),
  ('pandas.io.formats',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\io\\formats\\__init__.py',
   'PYMODULE-1'),
  ('pandas.io.formats._color_data',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\io\\formats\\_color_data.py',
   'PYMODULE-1'),
  ('pandas.io.formats.console',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\io\\formats\\console.py',
   'PYMODULE-1'),
  ('pandas.io.formats.css',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\io\\formats\\css.py',
   'PYMODULE-1'),
  ('pandas.io.formats.csvs',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\io\\formats\\csvs.py',
   'PYMODULE-1'),
  ('pandas.io.formats.excel',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\io\\formats\\excel.py',
   'PYMODULE-1'),
  ('pandas.io.formats.format',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\io\\formats\\format.py',
   'PYMODULE-1'),
  ('pandas.io.formats.html',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\io\\formats\\html.py',
   'PYMODULE-1'),
  ('pandas.io.formats.info',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\io\\formats\\info.py',
   'PYMODULE-1'),
  ('pandas.io.formats.printing',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\io\\formats\\printing.py',
   'PYMODULE-1'),
  ('pandas.io.formats.string',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\io\\formats\\string.py',
   'PYMODULE-1'),
  ('pandas.io.formats.style',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\io\\formats\\style.py',
   'PYMODULE-1'),
  ('pandas.io.formats.style_render',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\io\\formats\\style_render.py',
   'PYMODULE-1'),
  ('pandas.io.formats.xml',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\io\\formats\\xml.py',
   'PYMODULE-1'),
  ('pandas.io.gbq',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\io\\gbq.py',
   'PYMODULE-1'),
  ('pandas.io.html',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\io\\html.py',
   'PYMODULE-1'),
  ('pandas.io.json',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\io\\json\\__init__.py',
   'PYMODULE-1'),
  ('pandas.io.json._json',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\io\\json\\_json.py',
   'PYMODULE-1'),
  ('pandas.io.json._normalize',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\io\\json\\_normalize.py',
   'PYMODULE-1'),
  ('pandas.io.json._table_schema',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\io\\json\\_table_schema.py',
   'PYMODULE-1'),
  ('pandas.io.orc',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\io\\orc.py',
   'PYMODULE-1'),
  ('pandas.io.parquet',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\io\\parquet.py',
   'PYMODULE-1'),
  ('pandas.io.parsers',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\io\\parsers\\__init__.py',
   'PYMODULE-1'),
  ('pandas.io.parsers.arrow_parser_wrapper',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\io\\parsers\\arrow_parser_wrapper.py',
   'PYMODULE-1'),
  ('pandas.io.parsers.base_parser',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\io\\parsers\\base_parser.py',
   'PYMODULE-1'),
  ('pandas.io.parsers.c_parser_wrapper',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\io\\parsers\\c_parser_wrapper.py',
   'PYMODULE-1'),
  ('pandas.io.parsers.python_parser',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\io\\parsers\\python_parser.py',
   'PYMODULE-1'),
  ('pandas.io.parsers.readers',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\io\\parsers\\readers.py',
   'PYMODULE-1'),
  ('pandas.io.pickle',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\io\\pickle.py',
   'PYMODULE-1'),
  ('pandas.io.pytables',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\io\\pytables.py',
   'PYMODULE-1'),
  ('pandas.io.sas',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\io\\sas\\__init__.py',
   'PYMODULE-1'),
  ('pandas.io.sas.sas7bdat',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\io\\sas\\sas7bdat.py',
   'PYMODULE-1'),
  ('pandas.io.sas.sas_constants',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\io\\sas\\sas_constants.py',
   'PYMODULE-1'),
  ('pandas.io.sas.sas_xport',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\io\\sas\\sas_xport.py',
   'PYMODULE-1'),
  ('pandas.io.sas.sasreader',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\io\\sas\\sasreader.py',
   'PYMODULE-1'),
  ('pandas.io.spss',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\io\\spss.py',
   'PYMODULE-1'),
  ('pandas.io.sql',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\io\\sql.py',
   'PYMODULE-1'),
  ('pandas.io.stata',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\io\\stata.py',
   'PYMODULE-1'),
  ('pandas.io.xml',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\io\\xml.py',
   'PYMODULE-1'),
  ('pandas.plotting',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\plotting\\__init__.py',
   'PYMODULE-1'),
  ('pandas.plotting._core',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\plotting\\_core.py',
   'PYMODULE-1'),
  ('pandas.plotting._matplotlib',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\plotting\\_matplotlib\\__init__.py',
   'PYMODULE-1'),
  ('pandas.plotting._matplotlib.boxplot',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\plotting\\_matplotlib\\boxplot.py',
   'PYMODULE-1'),
  ('pandas.plotting._matplotlib.converter',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\plotting\\_matplotlib\\converter.py',
   'PYMODULE-1'),
  ('pandas.plotting._matplotlib.core',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\plotting\\_matplotlib\\core.py',
   'PYMODULE-1'),
  ('pandas.plotting._matplotlib.groupby',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\plotting\\_matplotlib\\groupby.py',
   'PYMODULE-1'),
  ('pandas.plotting._matplotlib.hist',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\plotting\\_matplotlib\\hist.py',
   'PYMODULE-1'),
  ('pandas.plotting._matplotlib.misc',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\plotting\\_matplotlib\\misc.py',
   'PYMODULE-1'),
  ('pandas.plotting._matplotlib.style',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\plotting\\_matplotlib\\style.py',
   'PYMODULE-1'),
  ('pandas.plotting._matplotlib.timeseries',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\plotting\\_matplotlib\\timeseries.py',
   'PYMODULE-1'),
  ('pandas.plotting._matplotlib.tools',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\plotting\\_matplotlib\\tools.py',
   'PYMODULE-1'),
  ('pandas.plotting._misc',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\plotting\\_misc.py',
   'PYMODULE-1'),
  ('pandas.testing',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\testing.py',
   'PYMODULE-1'),
  ('pandas.tseries',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\tseries\\__init__.py',
   'PYMODULE-1'),
  ('pandas.tseries.api',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\tseries\\api.py',
   'PYMODULE-1'),
  ('pandas.tseries.frequencies',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\tseries\\frequencies.py',
   'PYMODULE-1'),
  ('pandas.tseries.holiday',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\tseries\\holiday.py',
   'PYMODULE-1'),
  ('pandas.tseries.offsets',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\tseries\\offsets.py',
   'PYMODULE-1'),
  ('pandas.util',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\util\\__init__.py',
   'PYMODULE-1'),
  ('pandas.util._decorators',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\util\\_decorators.py',
   'PYMODULE-1'),
  ('pandas.util._exceptions',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\util\\_exceptions.py',
   'PYMODULE-1'),
  ('pandas.util._print_versions',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\util\\_print_versions.py',
   'PYMODULE-1'),
  ('pandas.util._tester',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\util\\_tester.py',
   'PYMODULE-1'),
  ('pandas.util._validators',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\util\\_validators.py',
   'PYMODULE-1'),
  ('pandas.util.version',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pandas\\util\\version\\__init__.py',
   'PYMODULE-1'),
  ('pathlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\pathlib\\__init__.py',
   'PYMODULE-1'),
  ('pathlib._abc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\pathlib\\_abc.py',
   'PYMODULE-1'),
  ('pathlib._local',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\pathlib\\_local.py',
   'PYMODULE-1'),
  ('pdb',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\pdb.py',
   'PYMODULE-1'),
  ('permissions_manager',
   'E:\\desktop_stores_app\\permissions_manager.py',
   'PYMODULE-1'),
  ('pickle',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\pickle.py',
   'PYMODULE-1'),
  ('pickletools',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\pickletools.py',
   'PYMODULE-1'),
  ('pkgutil',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\pkgutil.py',
   'PYMODULE-1'),
  ('platform',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\platform.py',
   'PYMODULE-1'),
  ('plistlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\plistlib.py',
   'PYMODULE-1'),
  ('pprint',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\pprint.py',
   'PYMODULE-1'),
  ('py_compile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\py_compile.py',
   'PYMODULE-1'),
  ('pydoc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\pydoc.py',
   'PYMODULE-1'),
  ('pydoc_data',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\pydoc_data\\__init__.py',
   'PYMODULE-1'),
  ('pydoc_data.topics',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\pydoc_data\\topics.py',
   'PYMODULE-1'),
  ('pyparsing',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pyparsing\\__init__.py',
   'PYMODULE-1'),
  ('pyparsing.actions',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pyparsing\\actions.py',
   'PYMODULE-1'),
  ('pyparsing.common',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pyparsing\\common.py',
   'PYMODULE-1'),
  ('pyparsing.core',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pyparsing\\core.py',
   'PYMODULE-1'),
  ('pyparsing.diagram',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pyparsing\\diagram\\__init__.py',
   'PYMODULE-1'),
  ('pyparsing.exceptions',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pyparsing\\exceptions.py',
   'PYMODULE-1'),
  ('pyparsing.helpers',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pyparsing\\helpers.py',
   'PYMODULE-1'),
  ('pyparsing.results',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pyparsing\\results.py',
   'PYMODULE-1'),
  ('pyparsing.testing',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pyparsing\\testing.py',
   'PYMODULE-1'),
  ('pyparsing.unicode',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pyparsing\\unicode.py',
   'PYMODULE-1'),
  ('pyparsing.util',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pyparsing\\util.py',
   'PYMODULE-1'),
  ('pytz',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pytz\\__init__.py',
   'PYMODULE-1'),
  ('pytz.exceptions',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pytz\\exceptions.py',
   'PYMODULE-1'),
  ('pytz.lazy',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pytz\\lazy.py',
   'PYMODULE-1'),
  ('pytz.tzfile',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pytz\\tzfile.py',
   'PYMODULE-1'),
  ('pytz.tzinfo',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\pytz\\tzinfo.py',
   'PYMODULE-1'),
  ('queue',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\queue.py',
   'PYMODULE-1'),
  ('quopri',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\quopri.py',
   'PYMODULE-1'),
  ('random',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\random.py',
   'PYMODULE-1'),
  ('reportlab',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\reportlab\\__init__.py',
   'PYMODULE-1'),
  ('reportlab.graphics',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\reportlab\\graphics\\__init__.py',
   'PYMODULE-1'),
  ('reportlab.graphics.charts',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\reportlab\\graphics\\charts\\__init__.py',
   'PYMODULE-1'),
  ('reportlab.graphics.charts.areas',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\reportlab\\graphics\\charts\\areas.py',
   'PYMODULE-1'),
  ('reportlab.graphics.charts.legends',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\reportlab\\graphics\\charts\\legends.py',
   'PYMODULE-1'),
  ('reportlab.graphics.charts.piecharts',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\reportlab\\graphics\\charts\\piecharts.py',
   'PYMODULE-1'),
  ('reportlab.graphics.charts.textlabels',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\reportlab\\graphics\\charts\\textlabels.py',
   'PYMODULE-1'),
  ('reportlab.graphics.charts.utils',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\reportlab\\graphics\\charts\\utils.py',
   'PYMODULE-1'),
  ('reportlab.graphics.charts.utils3d',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\reportlab\\graphics\\charts\\utils3d.py',
   'PYMODULE-1'),
  ('reportlab.graphics.renderPDF',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\reportlab\\graphics\\renderPDF.py',
   'PYMODULE-1'),
  ('reportlab.graphics.renderPM',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\reportlab\\graphics\\renderPM.py',
   'PYMODULE-1'),
  ('reportlab.graphics.renderPS',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\reportlab\\graphics\\renderPS.py',
   'PYMODULE-1'),
  ('reportlab.graphics.renderSVG',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\reportlab\\graphics\\renderSVG.py',
   'PYMODULE-1'),
  ('reportlab.graphics.renderbase',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\reportlab\\graphics\\renderbase.py',
   'PYMODULE-1'),
  ('reportlab.graphics.shapes',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\reportlab\\graphics\\shapes.py',
   'PYMODULE-1'),
  ('reportlab.graphics.testshapes',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\reportlab\\graphics\\testshapes.py',
   'PYMODULE-1'),
  ('reportlab.graphics.transform',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\reportlab\\graphics\\transform.py',
   'PYMODULE-1'),
  ('reportlab.graphics.utils',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\reportlab\\graphics\\utils.py',
   'PYMODULE-1'),
  ('reportlab.graphics.widgetbase',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\reportlab\\graphics\\widgetbase.py',
   'PYMODULE-1'),
  ('reportlab.graphics.widgets',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\reportlab\\graphics\\widgets\\__init__.py',
   'PYMODULE-1'),
  ('reportlab.graphics.widgets.flags',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\reportlab\\graphics\\widgets\\flags.py',
   'PYMODULE-1'),
  ('reportlab.graphics.widgets.markers',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\reportlab\\graphics\\widgets\\markers.py',
   'PYMODULE-1'),
  ('reportlab.graphics.widgets.signsandsymbols',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\reportlab\\graphics\\widgets\\signsandsymbols.py',
   'PYMODULE-1'),
  ('reportlab.lib',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\reportlab\\lib\\__init__.py',
   'PYMODULE-1'),
  ('reportlab.lib.PyFontify',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\reportlab\\lib\\PyFontify.py',
   'PYMODULE-1'),
  ('reportlab.lib.abag',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\reportlab\\lib\\abag.py',
   'PYMODULE-1'),
  ('reportlab.lib.arciv',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\reportlab\\lib\\arciv.py',
   'PYMODULE-1'),
  ('reportlab.lib.attrmap',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\reportlab\\lib\\attrmap.py',
   'PYMODULE-1'),
  ('reportlab.lib.boxstuff',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\reportlab\\lib\\boxstuff.py',
   'PYMODULE-1'),
  ('reportlab.lib.colors',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\reportlab\\lib\\colors.py',
   'PYMODULE-1'),
  ('reportlab.lib.corp',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\reportlab\\lib\\corp.py',
   'PYMODULE-1'),
  ('reportlab.lib.enums',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\reportlab\\lib\\enums.py',
   'PYMODULE-1'),
  ('reportlab.lib.fonts',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\reportlab\\lib\\fonts.py',
   'PYMODULE-1'),
  ('reportlab.lib.formatters',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\reportlab\\lib\\formatters.py',
   'PYMODULE-1'),
  ('reportlab.lib.geomutils',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\reportlab\\lib\\geomutils.py',
   'PYMODULE-1'),
  ('reportlab.lib.logger',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\reportlab\\lib\\logger.py',
   'PYMODULE-1'),
  ('reportlab.lib.normalDate',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\reportlab\\lib\\normalDate.py',
   'PYMODULE-1'),
  ('reportlab.lib.pagesizes',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\reportlab\\lib\\pagesizes.py',
   'PYMODULE-1'),
  ('reportlab.lib.pdfencrypt',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\reportlab\\lib\\pdfencrypt.py',
   'PYMODULE-1'),
  ('reportlab.lib.randomtext',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\reportlab\\lib\\randomtext.py',
   'PYMODULE-1'),
  ('reportlab.lib.rl_accel',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\reportlab\\lib\\rl_accel.py',
   'PYMODULE-1'),
  ('reportlab.lib.rl_safe_eval',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\reportlab\\lib\\rl_safe_eval.py',
   'PYMODULE-1'),
  ('reportlab.lib.rltempfile',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\reportlab\\lib\\rltempfile.py',
   'PYMODULE-1'),
  ('reportlab.lib.rparsexml',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\reportlab\\lib\\rparsexml.py',
   'PYMODULE-1'),
  ('reportlab.lib.sequencer',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\reportlab\\lib\\sequencer.py',
   'PYMODULE-1'),
  ('reportlab.lib.styles',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\reportlab\\lib\\styles.py',
   'PYMODULE-1'),
  ('reportlab.lib.textsplit',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\reportlab\\lib\\textsplit.py',
   'PYMODULE-1'),
  ('reportlab.lib.units',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\reportlab\\lib\\units.py',
   'PYMODULE-1'),
  ('reportlab.lib.utils',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\reportlab\\lib\\utils.py',
   'PYMODULE-1'),
  ('reportlab.lib.validators',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\reportlab\\lib\\validators.py',
   'PYMODULE-1'),
  ('reportlab.pdfbase',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\reportlab\\pdfbase\\__init__.py',
   'PYMODULE-1'),
  ('reportlab.pdfbase._fontdata',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\reportlab\\pdfbase\\_fontdata.py',
   'PYMODULE-1'),
  ('reportlab.pdfbase._fontdata_enc_macexpert',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\reportlab\\pdfbase\\_fontdata_enc_macexpert.py',
   'PYMODULE-1'),
  ('reportlab.pdfbase._fontdata_enc_macroman',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\reportlab\\pdfbase\\_fontdata_enc_macroman.py',
   'PYMODULE-1'),
  ('reportlab.pdfbase._fontdata_enc_pdfdoc',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\reportlab\\pdfbase\\_fontdata_enc_pdfdoc.py',
   'PYMODULE-1'),
  ('reportlab.pdfbase._fontdata_enc_standard',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\reportlab\\pdfbase\\_fontdata_enc_standard.py',
   'PYMODULE-1'),
  ('reportlab.pdfbase._fontdata_enc_symbol',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\reportlab\\pdfbase\\_fontdata_enc_symbol.py',
   'PYMODULE-1'),
  ('reportlab.pdfbase._fontdata_enc_winansi',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\reportlab\\pdfbase\\_fontdata_enc_winansi.py',
   'PYMODULE-1'),
  ('reportlab.pdfbase._fontdata_enc_zapfdingbats',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\reportlab\\pdfbase\\_fontdata_enc_zapfdingbats.py',
   'PYMODULE-1'),
  ('reportlab.pdfbase._fontdata_widths_courier',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\reportlab\\pdfbase\\_fontdata_widths_courier.py',
   'PYMODULE-1'),
  ('reportlab.pdfbase._fontdata_widths_courierbold',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\reportlab\\pdfbase\\_fontdata_widths_courierbold.py',
   'PYMODULE-1'),
  ('reportlab.pdfbase._fontdata_widths_courierboldoblique',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\reportlab\\pdfbase\\_fontdata_widths_courierboldoblique.py',
   'PYMODULE-1'),
  ('reportlab.pdfbase._fontdata_widths_courieroblique',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\reportlab\\pdfbase\\_fontdata_widths_courieroblique.py',
   'PYMODULE-1'),
  ('reportlab.pdfbase._fontdata_widths_helvetica',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\reportlab\\pdfbase\\_fontdata_widths_helvetica.py',
   'PYMODULE-1'),
  ('reportlab.pdfbase._fontdata_widths_helveticabold',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\reportlab\\pdfbase\\_fontdata_widths_helveticabold.py',
   'PYMODULE-1'),
  ('reportlab.pdfbase._fontdata_widths_helveticaboldoblique',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\reportlab\\pdfbase\\_fontdata_widths_helveticaboldoblique.py',
   'PYMODULE-1'),
  ('reportlab.pdfbase._fontdata_widths_helveticaoblique',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\reportlab\\pdfbase\\_fontdata_widths_helveticaoblique.py',
   'PYMODULE-1'),
  ('reportlab.pdfbase._fontdata_widths_symbol',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\reportlab\\pdfbase\\_fontdata_widths_symbol.py',
   'PYMODULE-1'),
  ('reportlab.pdfbase._fontdata_widths_timesbold',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\reportlab\\pdfbase\\_fontdata_widths_timesbold.py',
   'PYMODULE-1'),
  ('reportlab.pdfbase._fontdata_widths_timesbolditalic',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\reportlab\\pdfbase\\_fontdata_widths_timesbolditalic.py',
   'PYMODULE-1'),
  ('reportlab.pdfbase._fontdata_widths_timesitalic',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\reportlab\\pdfbase\\_fontdata_widths_timesitalic.py',
   'PYMODULE-1'),
  ('reportlab.pdfbase._fontdata_widths_timesroman',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\reportlab\\pdfbase\\_fontdata_widths_timesroman.py',
   'PYMODULE-1'),
  ('reportlab.pdfbase._fontdata_widths_zapfdingbats',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\reportlab\\pdfbase\\_fontdata_widths_zapfdingbats.py',
   'PYMODULE-1'),
  ('reportlab.pdfbase._glyphlist',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\reportlab\\pdfbase\\_glyphlist.py',
   'PYMODULE-1'),
  ('reportlab.pdfbase.acroform',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\reportlab\\pdfbase\\acroform.py',
   'PYMODULE-1'),
  ('reportlab.pdfbase.pdfdoc',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\reportlab\\pdfbase\\pdfdoc.py',
   'PYMODULE-1'),
  ('reportlab.pdfbase.pdfmetrics',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\reportlab\\pdfbase\\pdfmetrics.py',
   'PYMODULE-1'),
  ('reportlab.pdfbase.pdfutils',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\reportlab\\pdfbase\\pdfutils.py',
   'PYMODULE-1'),
  ('reportlab.pdfbase.rl_codecs',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\reportlab\\pdfbase\\rl_codecs.py',
   'PYMODULE-1'),
  ('reportlab.pdfbase.ttfonts',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\reportlab\\pdfbase\\ttfonts.py',
   'PYMODULE-1'),
  ('reportlab.pdfgen',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\reportlab\\pdfgen\\__init__.py',
   'PYMODULE-1'),
  ('reportlab.pdfgen.canvas',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\reportlab\\pdfgen\\canvas.py',
   'PYMODULE-1'),
  ('reportlab.pdfgen.pathobject',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\reportlab\\pdfgen\\pathobject.py',
   'PYMODULE-1'),
  ('reportlab.pdfgen.pdfgeom',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\reportlab\\pdfgen\\pdfgeom.py',
   'PYMODULE-1'),
  ('reportlab.pdfgen.pdfimages',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\reportlab\\pdfgen\\pdfimages.py',
   'PYMODULE-1'),
  ('reportlab.pdfgen.textobject',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\reportlab\\pdfgen\\textobject.py',
   'PYMODULE-1'),
  ('reportlab.platypus',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\reportlab\\platypus\\__init__.py',
   'PYMODULE-1'),
  ('reportlab.platypus.doctemplate',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\reportlab\\platypus\\doctemplate.py',
   'PYMODULE-1'),
  ('reportlab.platypus.flowables',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\reportlab\\platypus\\flowables.py',
   'PYMODULE-1'),
  ('reportlab.platypus.frames',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\reportlab\\platypus\\frames.py',
   'PYMODULE-1'),
  ('reportlab.platypus.multicol',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\reportlab\\platypus\\multicol.py',
   'PYMODULE-1'),
  ('reportlab.platypus.paragraph',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\reportlab\\platypus\\paragraph.py',
   'PYMODULE-1'),
  ('reportlab.platypus.paraparser',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\reportlab\\platypus\\paraparser.py',
   'PYMODULE-1'),
  ('reportlab.platypus.tableofcontents',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\reportlab\\platypus\\tableofcontents.py',
   'PYMODULE-1'),
  ('reportlab.platypus.tables',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\reportlab\\platypus\\tables.py',
   'PYMODULE-1'),
  ('reportlab.platypus.xpreformatted',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\reportlab\\platypus\\xpreformatted.py',
   'PYMODULE-1'),
  ('reportlab.rl_config',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\reportlab\\rl_config.py',
   'PYMODULE-1'),
  ('reportlab.rl_settings',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\reportlab\\rl_settings.py',
   'PYMODULE-1'),
  ('rlcompleter',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\rlcompleter.py',
   'PYMODULE-1'),
  ('runpy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\runpy.py',
   'PYMODULE-1'),
  ('safe_window_manager',
   'E:\\desktop_stores_app\\safe_window_manager.py',
   'PYMODULE-1'),
  ('secrets',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\secrets.py',
   'PYMODULE-1'),
  ('selectors',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\selectors.py',
   'PYMODULE-1'),
  ('shlex',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\shlex.py',
   'PYMODULE-1'),
  ('shutil',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\shutil.py',
   'PYMODULE-1'),
  ('signal',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\signal.py',
   'PYMODULE-1'),
  ('six',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\six.py',
   'PYMODULE-1'),
  ('smtplib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\smtplib.py',
   'PYMODULE-1'),
  ('socket',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\socket.py',
   'PYMODULE-1'),
  ('socketserver',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\socketserver.py',
   'PYMODULE-1'),
  ('sqlite3',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\sqlite3\\__init__.py',
   'PYMODULE-1'),
  ('sqlite3.__main__',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\sqlite3\\__main__.py',
   'PYMODULE-1'),
  ('sqlite3.dbapi2',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\sqlite3\\dbapi2.py',
   'PYMODULE-1'),
  ('sqlite3.dump',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\sqlite3\\dump.py',
   'PYMODULE-1'),
  ('ssl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\ssl.py',
   'PYMODULE-1'),
  ('statistics',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\statistics.py',
   'PYMODULE-1'),
  ('string',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\string.py',
   'PYMODULE-1'),
  ('stringprep',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\stringprep.py',
   'PYMODULE-1'),
  ('subprocess',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\subprocess.py',
   'PYMODULE-1'),
  ('sysconfig',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\sysconfig\\__init__.py',
   'PYMODULE-1'),
  ('tarfile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\tarfile.py',
   'PYMODULE-1'),
  ('tempfile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\tempfile.py',
   'PYMODULE-1'),
  ('textwrap',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\textwrap.py',
   'PYMODULE-1'),
  ('threading',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\threading.py',
   'PYMODULE-1'),
  ('tkinter',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\tkinter\\__init__.py',
   'PYMODULE-1'),
  ('tkinter.colorchooser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\tkinter\\colorchooser.py',
   'PYMODULE-1'),
  ('tkinter.commondialog',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\tkinter\\commondialog.py',
   'PYMODULE-1'),
  ('tkinter.constants',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\tkinter\\constants.py',
   'PYMODULE-1'),
  ('tkinter.dialog',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\tkinter\\dialog.py',
   'PYMODULE-1'),
  ('tkinter.filedialog',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\tkinter\\filedialog.py',
   'PYMODULE-1'),
  ('tkinter.font',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\tkinter\\font.py',
   'PYMODULE-1'),
  ('tkinter.messagebox',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\tkinter\\messagebox.py',
   'PYMODULE-1'),
  ('tkinter.scrolledtext',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\tkinter\\scrolledtext.py',
   'PYMODULE-1'),
  ('tkinter.simpledialog',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\tkinter\\simpledialog.py',
   'PYMODULE-1'),
  ('tkinter.ttk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\tkinter\\ttk.py',
   'PYMODULE-1'),
  ('token',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\token.py',
   'PYMODULE-1'),
  ('tokenize',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\tokenize.py',
   'PYMODULE-1'),
  ('tracemalloc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\tracemalloc.py',
   'PYMODULE-1'),
  ('ttkbootstrap',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\ttkbootstrap\\__init__.py',
   'PYMODULE-1'),
  ('ttkbootstrap.colorutils',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\ttkbootstrap\\colorutils.py',
   'PYMODULE-1'),
  ('ttkbootstrap.constants',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\ttkbootstrap\\constants.py',
   'PYMODULE-1'),
  ('ttkbootstrap.dialogs',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\ttkbootstrap\\dialogs\\__init__.py',
   'PYMODULE-1'),
  ('ttkbootstrap.dialogs.colorchooser',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\ttkbootstrap\\dialogs\\colorchooser.py',
   'PYMODULE-1'),
  ('ttkbootstrap.dialogs.colordropper',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\ttkbootstrap\\dialogs\\colordropper.py',
   'PYMODULE-1'),
  ('ttkbootstrap.dialogs.dialogs',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\ttkbootstrap\\dialogs\\dialogs.py',
   'PYMODULE-1'),
  ('ttkbootstrap.icons',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\ttkbootstrap\\icons.py',
   'PYMODULE-1'),
  ('ttkbootstrap.localization',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\ttkbootstrap\\localization\\__init__.py',
   'PYMODULE-1'),
  ('ttkbootstrap.localization.msgcat',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\ttkbootstrap\\localization\\msgcat.py',
   'PYMODULE-1'),
  ('ttkbootstrap.localization.msgs',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\ttkbootstrap\\localization\\msgs.py',
   'PYMODULE-1'),
  ('ttkbootstrap.publisher',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\ttkbootstrap\\publisher.py',
   'PYMODULE-1'),
  ('ttkbootstrap.scrolled',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\ttkbootstrap\\scrolled.py',
   'PYMODULE-1'),
  ('ttkbootstrap.style',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\ttkbootstrap\\style.py',
   'PYMODULE-1'),
  ('ttkbootstrap.tableview',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\ttkbootstrap\\tableview.py',
   'PYMODULE-1'),
  ('ttkbootstrap.themes',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\ttkbootstrap\\themes\\__init__.py',
   'PYMODULE-1'),
  ('ttkbootstrap.themes.standard',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\ttkbootstrap\\themes\\standard.py',
   'PYMODULE-1'),
  ('ttkbootstrap.themes.user',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\ttkbootstrap\\themes\\user.py',
   'PYMODULE-1'),
  ('ttkbootstrap.tooltip',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\ttkbootstrap\\tooltip.py',
   'PYMODULE-1'),
  ('ttkbootstrap.utility',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\ttkbootstrap\\utility.py',
   'PYMODULE-1'),
  ('ttkbootstrap.validation',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\ttkbootstrap\\validation.py',
   'PYMODULE-1'),
  ('ttkbootstrap.widgets',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\ttkbootstrap\\widgets.py',
   'PYMODULE-1'),
  ('ttkbootstrap.window',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\ttkbootstrap\\window.py',
   'PYMODULE-1'),
  ('tty',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\tty.py',
   'PYMODULE-1'),
  ('typing',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\typing.py',
   'PYMODULE-1'),
  ('tzdata',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\tzdata\\__init__.py',
   'PYMODULE-1'),
  ('tzdata.zoneinfo',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\tzdata\\zoneinfo\\__init__.py',
   'PYMODULE-1'),
  ('tzdata.zoneinfo.Africa',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\__init__.py',
   'PYMODULE-1'),
  ('tzdata.zoneinfo.America',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\__init__.py',
   'PYMODULE-1'),
  ('tzdata.zoneinfo.America.Argentina',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Argentina\\__init__.py',
   'PYMODULE-1'),
  ('tzdata.zoneinfo.America.Indiana',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Indiana\\__init__.py',
   'PYMODULE-1'),
  ('tzdata.zoneinfo.America.Kentucky',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Kentucky\\__init__.py',
   'PYMODULE-1'),
  ('tzdata.zoneinfo.America.North_Dakota',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\North_Dakota\\__init__.py',
   'PYMODULE-1'),
  ('tzdata.zoneinfo.Antarctica',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\tzdata\\zoneinfo\\Antarctica\\__init__.py',
   'PYMODULE-1'),
  ('tzdata.zoneinfo.Arctic',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\tzdata\\zoneinfo\\Arctic\\__init__.py',
   'PYMODULE-1'),
  ('tzdata.zoneinfo.Asia',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\__init__.py',
   'PYMODULE-1'),
  ('tzdata.zoneinfo.Atlantic',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\tzdata\\zoneinfo\\Atlantic\\__init__.py',
   'PYMODULE-1'),
  ('tzdata.zoneinfo.Australia',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\tzdata\\zoneinfo\\Australia\\__init__.py',
   'PYMODULE-1'),
  ('tzdata.zoneinfo.Brazil',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\tzdata\\zoneinfo\\Brazil\\__init__.py',
   'PYMODULE-1'),
  ('tzdata.zoneinfo.Canada',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\tzdata\\zoneinfo\\Canada\\__init__.py',
   'PYMODULE-1'),
  ('tzdata.zoneinfo.Chile',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\tzdata\\zoneinfo\\Chile\\__init__.py',
   'PYMODULE-1'),
  ('tzdata.zoneinfo.Etc',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\tzdata\\zoneinfo\\Etc\\__init__.py',
   'PYMODULE-1'),
  ('tzdata.zoneinfo.Europe',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\__init__.py',
   'PYMODULE-1'),
  ('tzdata.zoneinfo.Indian',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\tzdata\\zoneinfo\\Indian\\__init__.py',
   'PYMODULE-1'),
  ('tzdata.zoneinfo.Mexico',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\tzdata\\zoneinfo\\Mexico\\__init__.py',
   'PYMODULE-1'),
  ('tzdata.zoneinfo.Pacific',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\__init__.py',
   'PYMODULE-1'),
  ('tzdata.zoneinfo.US',
   'E:\\desktop_stores_app\\.venv\\Lib\\site-packages\\tzdata\\zoneinfo\\US\\__init__.py',
   'PYMODULE-1'),
  ('ui', 'E:\\desktop_stores_app\\ui\\__init__.py', 'PYMODULE-1'),
  ('ui.__init__', 'E:\\desktop_stores_app\\ui\\__init__.py', 'PYMODULE-1'),
  ('ui.add_inventory_movement_window',
   'E:\\desktop_stores_app\\ui\\add_inventory_movement_window.py',
   'PYMODULE-1'),
  ('ui.add_item_window',
   'E:\\desktop_stores_app\\ui\\add_item_window.py',
   'PYMODULE-1'),
  ('ui.add_organization_window',
   'E:\\desktop_stores_app\\ui\\add_organization_window.py',
   'PYMODULE-1'),
  ('ui.add_transaction_item_window',
   'E:\\desktop_stores_app\\ui\\add_transaction_item_window.py',
   'PYMODULE-1'),
  ('ui.add_user_simple',
   'E:\\desktop_stores_app\\ui\\add_user_simple.py',
   'PYMODULE-1'),
  ('ui.autocomplete_entry',
   'E:\\desktop_stores_app\\ui\\autocomplete_entry.py',
   'PYMODULE-1'),
  ('ui.beneficiaries_window',
   'E:\\desktop_stores_app\\ui\\beneficiaries_window.py',
   'PYMODULE-1'),
  ('ui.departments_window',
   'E:\\desktop_stores_app\\ui\\departments_window.py',
   'PYMODULE-1'),
  ('ui.edit_inventory_movement_window',
   'E:\\desktop_stores_app\\ui\\edit_inventory_movement_window.py',
   'PYMODULE-1'),
  ('ui.edit_item_window',
   'E:\\desktop_stores_app\\ui\\edit_item_window.py',
   'PYMODULE-1'),
  ('ui.edit_transaction_item_window',
   'E:\\desktop_stores_app\\ui\\edit_transaction_item_window.py',
   'PYMODULE-1'),
  ('ui.edit_transaction_window',
   'E:\\desktop_stores_app\\ui\\edit_transaction_window.py',
   'PYMODULE-1'),
  ('ui.enhanced_transactions_report',
   'E:\\desktop_stores_app\\ui\\enhanced_transactions_report.py',
   'PYMODULE-1'),
  ('ui.inventory_dashboard_window',
   'E:\\desktop_stores_app\\ui\\inventory_dashboard_window.py',
   'PYMODULE-1'),
  ('ui.inventory_movements_window',
   'E:\\desktop_stores_app\\ui\\inventory_movements_window.py',
   'PYMODULE-1'),
  ('ui.inventory_standalone_window',
   'E:\\desktop_stores_app\\ui\\inventory_standalone_window.py',
   'PYMODULE-1'),
  ('ui.inventory_status_window',
   'E:\\desktop_stores_app\\ui\\inventory_status_window.py',
   'PYMODULE-1'),
  ('ui.inventory_window',
   'E:\\desktop_stores_app\\ui\\inventory_window.py',
   'PYMODULE-1'),
  ('ui.item_edit_window',
   'E:\\desktop_stores_app\\ui\\item_edit_window.py',
   'PYMODULE-1'),
  ('ui.item_preview_window',
   'E:\\desktop_stores_app\\ui\\item_preview_window.py',
   'PYMODULE-1'),
  ('ui.login_window',
   'E:\\desktop_stores_app\\ui\\login_window.py',
   'PYMODULE-1'),
  ('ui.main_window',
   'E:\\desktop_stores_app\\ui\\main_window.py',
   'PYMODULE-1'),
  ('ui.new_login_window',
   'E:\\desktop_stores_app\\ui\\new_login_window.py',
   'PYMODULE-1'),
  ('ui.new_transaction_window',
   'E:\\desktop_stores_app\\ui\\new_transaction_window.py',
   'PYMODULE-1'),
  ('ui.organizational_chart_add_ultra_simple',
   'E:\\desktop_stores_app\\ui\\organizational_chart_add_ultra_simple.py',
   'PYMODULE-1'),
  ('ui.organizational_chart_advanced_window',
   'E:\\desktop_stores_app\\ui\\organizational_chart_advanced_window.py',
   'PYMODULE-1'),
  ('ui.organizational_chart_window',
   'E:\\desktop_stores_app\\ui\\organizational_chart_window.py',
   'PYMODULE-1'),
  ('ui.reports_window',
   'E:\\desktop_stores_app\\ui\\reports_window.py',
   'PYMODULE-1'),
  ('ui.sections_window',
   'E:\\desktop_stores_app\\ui\\sections_window.py',
   'PYMODULE-1'),
  ('ui.settings_window',
   'E:\\desktop_stores_app\\ui\\settings_window.py',
   'PYMODULE-1'),
  ('ui.simple_login_window',
   'E:\\desktop_stores_app\\ui\\simple_login_window.py',
   'PYMODULE-1'),
  ('ui.splash_screen',
   'E:\\desktop_stores_app\\ui\\splash_screen.py',
   'PYMODULE-1'),
  ('ui.success_message',
   'E:\\desktop_stores_app\\ui\\success_message.py',
   'PYMODULE-1'),
  ('ui.system_specs_window',
   'E:\\desktop_stores_app\\ui\\system_specs_window.py',
   'PYMODULE-1'),
  ('ui.transaction_details_window',
   'E:\\desktop_stores_app\\ui\\transaction_details_window.py',
   'PYMODULE-1'),
  ('ui.transactions_report_window',
   'E:\\desktop_stores_app\\ui\\transactions_report_window.py',
   'PYMODULE-1'),
  ('ui.transactions_window',
   'E:\\desktop_stores_app\\ui\\transactions_window.py',
   'PYMODULE-1'),
  ('ui.units_window',
   'E:\\desktop_stores_app\\ui\\units_window.py',
   'PYMODULE-1'),
  ('ui.user_guide_window',
   'E:\\desktop_stores_app\\ui\\user_guide_window.py',
   'PYMODULE-1'),
  ('ui.users_management_window',
   'E:\\desktop_stores_app\\ui\\users_management_window.py',
   'PYMODULE-1'),
  ('urllib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\urllib\\__init__.py',
   'PYMODULE-1'),
  ('urllib.error',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\urllib\\error.py',
   'PYMODULE-1'),
  ('urllib.parse',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\urllib\\parse.py',
   'PYMODULE-1'),
  ('urllib.request',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\urllib\\request.py',
   'PYMODULE-1'),
  ('urllib.response',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\urllib\\response.py',
   'PYMODULE-1'),
  ('utils', 'E:\\desktop_stores_app\\utils\\__init__.py', 'PYMODULE-1'),
  ('utils.__init__',
   'E:\\desktop_stores_app\\utils\\__init__.py',
   'PYMODULE-1'),
  ('utils.backup_manager',
   'E:\\desktop_stores_app\\utils\\backup_manager.py',
   'PYMODULE-1'),
  ('utils.excel_manager',
   'E:\\desktop_stores_app\\utils\\excel_manager.py',
   'PYMODULE-1'),
  ('utils.logger', 'E:\\desktop_stores_app\\utils\\logger.py', 'PYMODULE-1'),
  ('utils.pdf_generator',
   'E:\\desktop_stores_app\\utils\\pdf_generator.py',
   'PYMODULE-1'),
  ('utils.print_manager',
   'E:\\desktop_stores_app\\utils\\print_manager.py',
   'PYMODULE-1'),
  ('utils.transaction_pdf',
   'E:\\desktop_stores_app\\utils\\transaction_pdf.py',
   'PYMODULE-1'),
  ('utils.window_utils',
   'E:\\desktop_stores_app\\utils\\window_utils.py',
   'PYMODULE-1'),
  ('uuid',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\uuid.py',
   'PYMODULE-1'),
  ('webbrowser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\webbrowser.py',
   'PYMODULE-1'),
  ('window_manager', 'E:\\desktop_stores_app\\window_manager.py', 'PYMODULE-1'),
  ('xml',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xml\\__init__.py',
   'PYMODULE-1'),
  ('xml.dom',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xml\\dom\\__init__.py',
   'PYMODULE-1'),
  ('xml.dom.NodeFilter',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xml\\dom\\NodeFilter.py',
   'PYMODULE-1'),
  ('xml.dom.domreg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xml\\dom\\domreg.py',
   'PYMODULE-1'),
  ('xml.dom.expatbuilder',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xml\\dom\\expatbuilder.py',
   'PYMODULE-1'),
  ('xml.dom.minicompat',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xml\\dom\\minicompat.py',
   'PYMODULE-1'),
  ('xml.dom.minidom',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xml\\dom\\minidom.py',
   'PYMODULE-1'),
  ('xml.dom.pulldom',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xml\\dom\\pulldom.py',
   'PYMODULE-1'),
  ('xml.dom.xmlbuilder',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xml\\dom\\xmlbuilder.py',
   'PYMODULE-1'),
  ('xml.etree',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xml\\etree\\__init__.py',
   'PYMODULE-1'),
  ('xml.etree.ElementInclude',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xml\\etree\\ElementInclude.py',
   'PYMODULE-1'),
  ('xml.etree.ElementPath',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xml\\etree\\ElementPath.py',
   'PYMODULE-1'),
  ('xml.etree.ElementTree',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xml\\etree\\ElementTree.py',
   'PYMODULE-1'),
  ('xml.etree.cElementTree',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xml\\etree\\cElementTree.py',
   'PYMODULE-1'),
  ('xml.parsers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xml\\parsers\\__init__.py',
   'PYMODULE-1'),
  ('xml.parsers.expat',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xml\\parsers\\expat.py',
   'PYMODULE-1'),
  ('xml.sax',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xml\\sax\\__init__.py',
   'PYMODULE-1'),
  ('xml.sax._exceptions',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xml\\sax\\_exceptions.py',
   'PYMODULE-1'),
  ('xml.sax.expatreader',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xml\\sax\\expatreader.py',
   'PYMODULE-1'),
  ('xml.sax.handler',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xml\\sax\\handler.py',
   'PYMODULE-1'),
  ('xml.sax.saxutils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xml\\sax\\saxutils.py',
   'PYMODULE-1'),
  ('xml.sax.xmlreader',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xml\\sax\\xmlreader.py',
   'PYMODULE-1'),
  ('xmlrpc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xmlrpc\\__init__.py',
   'PYMODULE-1'),
  ('xmlrpc.client',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xmlrpc\\client.py',
   'PYMODULE-1'),
  ('zipfile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\zipfile\\__init__.py',
   'PYMODULE-1'),
  ('zipfile._path',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\zipfile\\_path\\__init__.py',
   'PYMODULE-1'),
  ('zipfile._path.glob',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\zipfile\\_path\\glob.py',
   'PYMODULE-1'),
  ('zipimport',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\zipimport.py',
   'PYMODULE-1'),
  ('zoneinfo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\zoneinfo\\__init__.py',
   'PYMODULE-1'),
  ('zoneinfo._common',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\zoneinfo\\_common.py',
   'PYMODULE-1'),
  ('zoneinfo._tzpath',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\zoneinfo\\_tzpath.py',
   'PYMODULE-1'),
  ('zoneinfo._zoneinfo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\zoneinfo\\_zoneinfo.py',
   'PYMODULE-1')])
