"""
Build script to create a complete executable with all dependencies using PyInstaller.
The executable will be named in Arabic and output to the dist folder with all libraries included.

Usage:
    python -m pip install pyinstaller
    python build_complete_exe.py

This script creates a complete standalone executable with all dependencies.
"""

import subprocess
import sys
import os
import shutil
import time
from pathlib import Path


def clean_build_dirs():
    """تنظيف مجلدات البناء السابقة"""
    dirs_to_clean = ['build', 'dist']
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            try:
                shutil.rmtree(dir_name)
                print(f"تم حذف مجلد: {dir_name}")
            except Exception as e:
                print(f"خطأ في حذف مجلد {dir_name}: {e}")


def install_requirements():
    """تثبيت المتطلبات المطلوبة"""
    print("تثبيت المتطلبات...")
    try:
        # تثبيت PyInstaller
        subprocess.run([sys.executable, '-m', 'pip', 'install', 'pyinstaller'], check=True)

        # تثبيت المتطلبات من ملف requirements.txt
        if os.path.exists('requirements.txt'):
            subprocess.run([sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'], check=True)

        print("تم تثبيت جميع المتطلبات بنجاح")
        return True
    except subprocess.CalledProcessError as e:
        print(f"خطأ في تثبيت المتطلبات: {e}")
        return False


def build_exe():
    """بناء ملف exe كامل"""
    try:
        print("بدء بناء ملف exe كامل...")
        print("=" * 50)

        # تنظيف المجلدات السابقة
        clean_build_dirs()

        # تثبيت المتطلبات
        if not install_requirements():
            return False

        # بناء الملف التنفيذي
        print("بناء الملف التنفيذي...")
        result = subprocess.run(
            [sys.executable, '-m', 'PyInstaller', 'complete_exe.spec', '--clean'],
            check=True, capture_output=True, text=True
        )

        print("تفاصيل البناء:")
        print(result.stdout)

        # التحقق من وجود الملف المبني
        exe_path = Path('dist/نظام_إدارة_المخازن_كامل.exe')
        if exe_path.exists():
            file_size = exe_path.stat().st_size / (1024 * 1024)  # بالميجابايت
            print("=" * 50)
            print("✅ تم بناء الملف التنفيذي بنجاح!")
            print(f"📁 المسار: {exe_path}")
            print(f"📊 حجم الملف: {file_size:.2f} ميجابايت")
            print("=" * 50)

            # إنشاء مجلد البرنامج الكامل
            create_complete_package()

            return True
        else:
            print("❌ فشل في إنشاء الملف التنفيذي")
            return False

    except subprocess.CalledProcessError as e:
        print("خطأ أثناء عملية البناء:")
        print("STDOUT:", e.stdout)
        print("STDERR:", e.stderr)
        return False
    except Exception as e:
        print(f"خطأ غير متوقع: {e}")
        return False


def create_complete_package():
    """إنشاء حزمة كاملة مع جميع الملفات المطلوبة"""
    try:
        package_name = f"نظام_إدارة_المخازن_كامل_{time.strftime('%Y%m%d_%H%M%S')}"
        package_dir = Path('dist') / package_name

        # إنشاء مجلد الحزمة
        package_dir.mkdir(parents=True, exist_ok=True)

        # نسخ الملف التنفيذي
        exe_source = Path('dist/نظام_إدارة_المخازن_كامل.exe')
        if exe_source.exists():
            shutil.copy2(exe_source, package_dir / 'نظام_إدارة_المخازن.exe')

        # نسخ الملفات الأساسية
        essential_files = [
            'settings.json',
            'create_tables.sql',
            'README.md' if os.path.exists('README.md') else None
        ]

        for file_name in essential_files:
            if file_name and os.path.exists(file_name):
                shutil.copy2(file_name, package_dir)

        # نسخ المجلدات الأساسية
        essential_dirs = ['assets', 'data', 'reports', 'backups', 'logs']
        for dir_name in essential_dirs:
            if os.path.exists(dir_name):
                shutil.copytree(dir_name, package_dir / dir_name, dirs_exist_ok=True)

        # إنشاء ملف تعليمات
        create_instructions_file(package_dir)

        print(f"✅ تم إنشاء الحزمة الكاملة: {package_dir}")

    except Exception as e:
        print(f"خطأ في إنشاء الحزمة الكاملة: {e}")


def create_instructions_file(package_dir):
    """إنشاء ملف تعليمات الاستخدام"""
    instructions = """
# نظام إدارة المخازن - تعليمات الاستخدام

## تشغيل البرنامج
- انقر نقراً مزدوجاً على ملف "نظام_إدارة_المخازن.exe"
- سيتم تشغيل البرنامج مباشرة بدون الحاجة لتثبيت أي مكتبات إضافية

## بيانات تسجيل الدخول الافتراضية
- اسم المستخدم: admin
- كلمة المرور: admin

## المجلدات المهمة
- assets: الأيقونات والصور
- data: قاعدة البيانات والملفات المحفوظة
- reports: التقارير المُصدرة
- backups: النسخ الاحتياطية
- logs: ملفات السجلات

## ملاحظات مهمة
- تأكد من وجود صلاحيات الكتابة في مجلد البرنامج
- يُنصح بعمل نسخة احتياطية دورية من مجلد data
- في حالة وجود مشاكل، راجع ملفات السجلات في مجلد logs

## الدعم الفني
للحصول على الدعم الفني أو الإبلاغ عن مشاكل، يرجى التواصل مع فريق التطوير.

تاريخ الإنشاء: {date}
إصدار البرنامج: 2.0
""".format(date=time.strftime('%Y-%m-%d %H:%M:%S'))

    instructions_file = package_dir / 'تعليمات_الاستخدام.txt'
    with open(instructions_file, 'w', encoding='utf-8') as f:
        f.write(instructions)


if __name__ == "__main__":
    print("🚀 بدء عملية بناء نظام إدارة المخازن الكامل")
    print("=" * 60)

    success = build_exe()

    if success:
        print("\n🎉 تمت عملية البناء بنجاح!")
        print("📂 تحقق من مجلد 'dist' للحصول على الملفات المبنية")
    else:
        print("\n❌ فشلت عملية البناء")
        print("🔍 راجع الأخطاء أعلاه لمعرفة السبب")

    print("=" * 60)
