# -*- mode: python ; coding: utf-8 -*-
# ملف إنشاء EXE كامل مع جميع المكتبات والملفات

import os
import sys
from pathlib import Path
import glob

# تحديد مسار المشروع
project_root = Path.cwd()
sys.path.insert(0, str(project_root))

# تحديد جميع الملفات والمجلدات المطلوبة
datas = []

# إضافة جميع المجلدات الأساسية
essential_dirs = [
    'ui', 'utils', 'assets', 'data', 'reports', 'backups', 
    'logs', 'exports', 'icons'
]

for dir_name in essential_dirs:
    if os.path.exists(dir_name):
        datas.append((dir_name, dir_name))
        print(f"إضافة مجلد: {dir_name}")

# إضافة جميع ملفات قاعدة البيانات والإعدادات
file_patterns = [
    '*.db', '*.sqlite', '*.sqlite3',
    '*.json', '*.txt', '*.md',
    '*.ico', '*.png', '*.jpg', '*.jpeg',
    '*.csv', '*.xlsx', '*.pdf'
]

for pattern in file_patterns:
    for file in glob.glob(pattern):
        if os.path.isfile(file):
            datas.append((file, '.'))
            print(f"إضافة ملف: {file}")

# إضافة ملفات Python الإضافية
python_modules = []
for root, dirs, files in os.walk('.'):
    dirs[:] = [d for d in dirs if d not in ['.git', '.vscode', '__pycache__', 'build', 'dist', '.venv']]
    for file in files:
        if file.endswith('.py') and file != 'main.py':
            module_path = os.path.join(root, file)
            python_modules.append(module_path)

# المكتبات المخفية الكاملة
hiddenimports = [
    # واجهة المستخدم
    'tkinter', 'tkinter.ttk', 'tkinter.messagebox', 'tkinter.filedialog',
    'tkinter.scrolledtext', 'tkinter.simpledialog', 'tkinter.colorchooser',
    'tkinter.font', 'tkinter.constants',
    'ttkbootstrap', 'ttkbootstrap.constants', 'ttkbootstrap.style',
    'ttkbootstrap.themes', 'ttkbootstrap.widgets', 'ttkbootstrap.dialogs',
    'ttkbootstrap.scrolled', 'ttkbootstrap.tableview', 'ttkbootstrap.tooltip',

    # معالجة الصور
    'PIL', 'PIL.Image', 'PIL.ImageTk', 'PIL.ImageDraw', 'PIL.ImageFont',
    'PIL.ImageOps', 'PIL.ImageFilter', 'PIL.ImageEnhance',

    # قاعدة البيانات
    'sqlite3', 'sqlalchemy', 'sqlalchemy.ext.declarative',
    'sqlalchemy.orm', 'sqlalchemy.sql', 'sqlalchemy.engine',
    'sqlalchemy.pool', 'sqlalchemy.dialects.sqlite',

    # المكتبات الأساسية
    'datetime', 'pathlib', 'threading', 'json', 'logging',
    'hashlib', 'uuid', 'base64', 'urllib', 'urllib.parse',
    'urllib.request', 'http.client', 'socket', 'ssl',
    'collections', 'itertools', 'functools', 'operator',

    # التقارير والملفات
    'openpyxl', 'openpyxl.workbook', 'openpyxl.worksheet', 'openpyxl.styles',
    'openpyxl.utils', 'openpyxl.chart', 'openpyxl.drawing',
    'reportlab', 'reportlab.pdfgen', 'reportlab.lib', 'reportlab.lib.pagesizes',
    'reportlab.lib.styles', 'reportlab.lib.colors', 'reportlab.platypus',
    'reportlab.pdfbase', 'reportlab.pdfbase.ttfonts',
    'pandas', 'pandas.io.excel', 'pandas.io.common', 'pandas.core',
    'xlsxwriter', 'xlrd', 'csv', 'io',

    # matplotlib للرسوم البيانية
    'matplotlib', 'matplotlib.pyplot', 'matplotlib.backends',
    'matplotlib.backends.backend_tkagg', 'matplotlib.figure',

    # الأمان والتشفير
    'bcrypt', 'cryptography', 'secrets', 'hmac',

    # التاريخ والوقت
    'calendar', 'time', 'locale', 'zoneinfo',

    # النظام والملفات
    'os', 'sys', 'shutil', 'glob', 'fnmatch',
    'tempfile', 'zipfile', 'tarfile', 'gzip',
    'subprocess', 'platform', 'ctypes',

    # الشبكة
    'webbrowser', 'email', 'smtplib', 'ftplib',

    # الرياضيات والإحصاء
    'math', 'statistics', 'random', 'decimal', 'fractions',

    # التعبيرات النمطية والنصوص
    're', 'string', 'textwrap', 'unicodedata',

    # وحدات التطبيق الأساسية
    'config', 'database', 'models', 'auth_manager',
    'font_manager', 'safe_window_manager', 'window_manager',
    'activity_monitor', 'permissions_manager',

    # جميع وحدات UI
    'ui', 'ui.__init__',
    'ui.main_window', 'ui.login_window', 'ui.new_login_window', 'ui.simple_login_window',
    'ui.splash_screen', 'ui.success_message',
    'ui.users_management_window', 'ui.add_user_simple',
    'ui.departments_window', 'ui.sections_window', 'ui.units_window',
    'ui.beneficiaries_window', 'ui.add_organization_window',
    'ui.organizational_chart_window', 'ui.organizational_chart_add_ultra_simple',
    'ui.organizational_chart_advanced_window',
    'ui.inventory_window', 'ui.inventory_dashboard_window', 'ui.inventory_standalone_window',
    'ui.inventory_movements_window', 'ui.add_inventory_movement_window',
    'ui.edit_inventory_movement_window', 'ui.inventory_status_window',
    'ui.add_item_window', 'ui.edit_item_window', 'ui.item_edit_window', 'ui.item_preview_window',
    'ui.transactions_window', 'ui.new_transaction_window', 'ui.edit_transaction_window',
    'ui.transaction_details_window', 'ui.add_transaction_item_window',
    'ui.edit_transaction_item_window',
    'ui.reports_window', 'ui.transactions_report_window', 'ui.enhanced_transactions_report',
    'ui.settings_window', 'ui.system_specs_window', 'ui.user_guide_window',
    'ui.autocomplete_entry',

    # جميع وحدات Utils
    'utils', 'utils.__init__',
    'utils.logger', 'utils.backup_manager', 'utils.excel_manager',
    'utils.pdf_generator', 'utils.print_manager', 'utils.transaction_pdf',
    'utils.window_utils',
]

# تحليل الملف الرئيسي
a = Analysis(
    ['main.py'],
    pathex=[str(project_root)],
    binaries=[],
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={
        'matplotlib': {
            'backends': ['TkAgg'],
        },
    },
    runtime_hooks=[],
    excludes=[
        # استبعاد الملفات غير المطلوبة فقط
        'test_*', 'tests', 'pytest', 'unittest',
        '.git', '.vscode', '.idea', '.pytest_cache',
        'numpy.tests', 'pandas.tests', 'matplotlib.tests',
        'PIL.tests', 'setuptools', 'distutils',
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=None,
    noarchive=False,
    optimize=1,
)

# إضافة جميع ملفات Python كوحدات مخفية
for py_file in python_modules:
    module_name = py_file.replace('\\', '.').replace('/', '.').replace('.py', '').lstrip('.')
    if module_name and module_name not in hiddenimports:
        a.hiddenimports.append(module_name)

pyz = PYZ(a.pure, a.zipped_data, cipher=None)

# إنشاء ملف تنفيذي واحد مستقل مع جميع المكتبات
exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='نظام_إدارة_المخازن_كامل',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=False,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    version=None,
    uac_admin=False,
    uac_uiaccess=False,
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    icon='assets/app_icon.ico' if os.path.exists('assets/app_icon.ico') else 'app_icon.ico' if os.path.exists('app_icon.ico') else None,
)
