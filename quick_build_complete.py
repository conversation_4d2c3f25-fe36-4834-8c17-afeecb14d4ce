#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
سكريبت سريع لبناء ملف exe كامل مع جميع المكتبات
Quick build script for complete exe with all libraries
"""

import subprocess
import sys
import os
import time
from pathlib import Path


def main():
    """الدالة الرئيسية لبناء ملف exe"""
    print("🚀 بدء بناء نظام إدارة المخازن الكامل")
    print("=" * 60)
    
    start_time = time.time()
    
    try:
        # التحقق من وجود الملفات المطلوبة
        required_files = ['main.py', 'complete_exe.spec']
        for file in required_files:
            if not os.path.exists(file):
                print(f"❌ الملف المطلوب غير موجود: {file}")
                return False
        
        # تشغيل سكريبت البناء الكامل
        print("📦 تشغيل سكريبت البناء الكامل...")
        result = subprocess.run(
            [sys.executable, 'build_complete_exe.py'],
            check=True,
            capture_output=False,
            text=True
        )
        
        # حساب الوقت المستغرق
        elapsed_time = time.time() - start_time
        minutes = int(elapsed_time // 60)
        seconds = int(elapsed_time % 60)
        
        print("\n" + "=" * 60)
        print("🎉 تمت عملية البناء بنجاح!")
        print(f"⏱️ الوقت المستغرق: {minutes} دقيقة و {seconds} ثانية")
        
        # التحقق من النتائج
        check_build_results()
        
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ خطأ في عملية البناء: {e}")
        return False
    except KeyboardInterrupt:
        print("\n⚠️ تم إيقاف العملية بواسطة المستخدم")
        return False
    except Exception as e:
        print(f"❌ خطأ غير متوقع: {e}")
        return False


def check_build_results():
    """التحقق من نتائج البناء"""
    dist_dir = Path('dist')
    
    if not dist_dir.exists():
        print("❌ مجلد dist غير موجود")
        return
    
    # البحث عن الملفات المبنية
    exe_files = list(dist_dir.glob('*.exe'))
    package_dirs = [d for d in dist_dir.iterdir() if d.is_dir()]
    
    print("\n📂 محتويات مجلد dist:")
    print("-" * 40)
    
    if exe_files:
        print("📄 ملفات EXE:")
        for exe_file in exe_files:
            size_mb = exe_file.stat().st_size / (1024 * 1024)
            print(f"  ✅ {exe_file.name} ({size_mb:.1f} MB)")
    
    if package_dirs:
        print("\n📁 حزم البرامج:")
        for package_dir in package_dirs:
            print(f"  📦 {package_dir.name}")
            
            # عرض محتويات الحزمة
            contents = list(package_dir.iterdir())
            if contents:
                print(f"     📋 يحتوي على {len(contents)} عنصر")
    
    print("\n" + "=" * 60)
    print("✨ جاهز للاستخدام!")
    print("📍 تحقق من مجلد 'dist' للحصول على الملفات النهائية")


if __name__ == "__main__":
    success = main()
    
    if success:
        print("\n🎯 العملية مكتملة بنجاح!")
        input("\n⏸️ اضغط Enter للخروج...")
    else:
        print("\n💥 فشلت العملية!")
        input("\n⏸️ اضغط Enter للخروج...")
