#!/usr/bin/env python3
"""
اختبار ميزة استيراد Excel مع تسجيل حركات المخزون التلقائية
Test Excel Import with Automatic Inventory Movement Registration
"""

import pandas as pd
import os
from datetime import datetime

def create_test_excel_file():
    """إنشاء ملف Excel تجريبي للاختبار"""
    
    # بيانات تجريبية للأصناف
    test_data = [
        {
            'الرقم': 1,
            'رقم الصنف': 'TEST001',
            'اسم الصنف': 'جهاز حاسب آلي تجريبي',
            'نوع العهدة': 'مستديمة',
            'التصنيف': 'أجهزة كهربائية',
            'الوحدة': 'عدد',
            'الكمية الحالية': 10
        },
        {
            'الرقم': 2,
            'رقم الصنف': 'TEST002',
            'اسم الصنف': 'طابعة ليزر تجريبية',
            'نوع العهدة': 'مستديمة',
            'التصنيف': 'أجهزة كهربائية',
            'الوحدة': 'عدد',
            'الكمية الحالية': 5
        },
        {
            'الرقم': 3,
            'رقم الصنف': 'TEST003',
            'اسم الصنف': 'كرسي مكتب تجريبي',
            'نوع العهدة': 'مستديمة',
            'التصنيف': 'أثاث مكتبي',
            'الوحدة': 'عدد',
            'الكمية الحالية': 20
        },
        {
            'الرقم': 4,
            'رقم الصنف': 'TEST004',
            'اسم الصنف': 'ورق A4 تجريبي',
            'نوع العهدة': 'استهلاكية',
            'التصنيف': 'قرطاسية',
            'الوحدة': 'رزمة',
            'الكمية الحالية': 100
        },
        {
            'الرقم': 5,
            'رقم الصنف': 'TEST005',
            'اسم الصنف': 'قلم حبر تجريبي',
            'نوع العهدة': 'استهلاكية',
            'التصنيف': 'قرطاسية',
            'الوحدة': 'عدد',
            'الكمية الحالية': 50
        }
    ]
    
    # إنشاء DataFrame
    df = pd.DataFrame(test_data)
    
    # حفظ الملف
    file_path = 'test_inventory_import.xlsx'
    df.to_excel(file_path, index=False, engine='openpyxl')
    
    print(f"✅ تم إنشاء ملف الاختبار: {file_path}")
    print(f"📊 عدد الأصناف: {len(test_data)}")
    print("📋 الأصناف المضافة:")
    for item in test_data:
        print(f"   - {item['رقم الصنف']}: {item['اسم الصنف']} ({item['الكمية الحالية']} {item['الوحدة']})")
    
    return file_path

def verify_excel_structure(file_path):
    """التحقق من هيكل ملف Excel"""
    try:
        df = pd.read_excel(file_path)
        
        print(f"\n🔍 التحقق من هيكل الملف: {file_path}")
        print(f"📊 عدد الصفوف: {len(df)}")
        print(f"📋 الأعمدة: {list(df.columns)}")
        
        # التحقق من الأعمدة المطلوبة
        required_columns = ['رقم الصنف', 'اسم الصنف', 'نوع العهدة', 'التصنيف', 'الوحدة', 'الكمية الحالية']
        missing_columns = [col for col in required_columns if col not in df.columns]
        
        if missing_columns:
            print(f"❌ أعمدة مفقودة: {missing_columns}")
            return False
        else:
            print("✅ جميع الأعمدة المطلوبة موجودة")
            return True
            
    except Exception as e:
        print(f"❌ خطأ في قراءة الملف: {e}")
        return False

def test_comprehensive_data_calculation():
    """اختبار حساب البيانات الشاملة"""
    print("\n🧪 اختبار حساب البيانات الشاملة...")
    
    # محاكاة بيانات الصنف
    test_item = {
        'item_number': 'TEST001',
        'item_name': 'جهاز حاسب آلي تجريبي',
        'entered_quantity': 10,  # الكمية المدخلة الأصلية
        'total_additions': 15,   # إجمالي الإضافات (10 أصلية + 5 إضافية)
        'total_dispensed': 3,    # إجمالي الصرف
        'actual_total': 12       # المجموع الفعلي (15 - 3)
    }
    
    print(f"📦 الصنف: {test_item['item_name']}")
    print(f"🔢 الكمية المدخلة الأصلية: {test_item['entered_quantity']}")
    print(f"➕ إجمالي الإضافات: {test_item['total_additions']}")
    print(f"➖ إجمالي الصرف: {test_item['total_dispensed']}")
    print(f"🎯 المجموع الفعلي: {test_item['actual_total']}")
    
    # التحقق من صحة الحساب
    calculated_total = test_item['total_additions'] - test_item['total_dispensed']
    if calculated_total == test_item['actual_total']:
        print("✅ حساب المجموع الفعلي صحيح")
    else:
        print(f"❌ خطأ في حساب المجموع الفعلي: متوقع {test_item['actual_total']}, محسوب {calculated_total}")

def main():
    """الدالة الرئيسية للاختبار"""
    print("🚀 بدء اختبار ميزة استيراد Excel مع تسجيل حركات المخزون")
    print("=" * 60)
    
    # إنشاء ملف Excel تجريبي
    excel_file = create_test_excel_file()
    
    # التحقق من هيكل الملف
    if verify_excel_structure(excel_file):
        print("✅ ملف Excel جاهز للاستيراد")
    else:
        print("❌ مشكلة في هيكل ملف Excel")
        return
    
    # اختبار حساب البيانات الشاملة
    test_comprehensive_data_calculation()
    
    print("\n📋 تعليمات الاختبار:")
    print("1. قم بتشغيل البرنامج الرئيسي")
    print("2. اذهب إلى شاشة إدارة الأصناف")
    print("3. اضغط على زر 'استيراد من Excel'")
    print(f"4. اختر الملف: {excel_file}")
    print("5. تحقق من إنشاء حركات المخزون التلقائية")
    print("6. اذهب إلى لوحة تحكم المخزون للتحقق من البيانات")
    print("7. اذهب إلى شاشة حركات المخزون لعرض التفاصيل الشاملة")
    
    print("\n🎯 النتائج المتوقعة:")
    print("- إضافة 5 أصناف جديدة")
    print("- إنشاء 5 حركات مخزون تلقائية (نوع: إضافة)")
    print("- عرض الكميات الصحيحة في لوحة التحكم")
    print("- إمكانية عرض التفاصيل الشاملة لكل صنف")

if __name__ == "__main__":
    main()
