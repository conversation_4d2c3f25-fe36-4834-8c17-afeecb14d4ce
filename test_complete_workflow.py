#!/usr/bin/env python3
"""
اختبار شامل لسير العمل الكامل
"""

import sys
from pathlib import Path
from datetime import datetime
import pandas as pd

# إضافة مسار المشروع
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from models import AddedItem

def test_complete_workflow():
    """اختبار سير العمل الكامل"""
    
    print("🔍 اختبار سير العمل الكامل...")
    
    # 1. إنشاء ملف Excel تجريبي
    print("\n📋 1. إنشاء ملف Excel تجريبي...")
    excel_data = {
        'الرقم': [1, 2, 3],
        'رقم الصنف': ['WORKFLOW_001', 'WORKFLOW_002', 'WORKFLOW_003'],
        'اسم الصنف': ['اختبار سير العمل 1', 'اختبار سير العمل 2', 'اختبار سير العمل 3'],
        'نوع العهدة': ['مستهلكة', 'مستديمة', 'مستهلكة'],
        'التصنيف': ['اختبار', 'اختبار', 'اختبار'],
        'الوحدة': ['عدد', 'عدد', 'عدد'],
        'الكمية الحالية': [150, 250, 350],
        'الكمية المدخلة': [150, 250, 350]
    }
    
    df = pd.DataFrame(excel_data)
    excel_file = 'test_workflow.xlsx'
    df.to_excel(excel_file, index=False, engine='openpyxl')
    print(f"✅ تم إنشاء ملف Excel: {excel_file}")
    
    # 2. محاكاة استيراد Excel
    print("\n📥 2. محاكاة استيراد Excel...")
    
    # قراءة الملف
    df_imported = pd.read_excel(excel_file, engine='openpyxl')
    print(f"📊 تم قراءة {len(df_imported)} صف من الملف")
    
    # حفظ البيانات في قاعدة البيانات
    for index, row in df_imported.iterrows():
        item = AddedItem(
            item_number=str(row['رقم الصنف']),
            item_name=str(row['اسم الصنف']),
            custody_type=str(row['نوع العهدة']),
            classification=str(row['التصنيف']),
            unit=str(row['الوحدة']),
            current_quantity=int(row['الكمية الحالية']),
            entered_quantity=int(row['الكمية المدخلة']),
            data_entry_user="مدير النظام",
            entry_date=datetime.now().strftime('%Y-%m-%d'),
            is_active=True
        )
        
        if item.save():
            print(f"✅ تم حفظ الصنف: {item.item_number} - {item.item_name}")
        else:
            print(f"❌ فشل في حفظ الصنف: {item.item_number}")
    
    # 3. التحقق من البيانات المحفوظة
    print("\n🔍 3. التحقق من البيانات المحفوظة...")
    
    for item_number in ['WORKFLOW_001', 'WORKFLOW_002', 'WORKFLOW_003']:
        saved_item = AddedItem.get_by_item_number(item_number)
        if saved_item:
            print(f"✅ {item_number}: الكمية الحالية={saved_item.current_quantity}, الكمية المدخلة={saved_item.entered_quantity}")
        else:
            print(f"❌ لم يتم العثور على الصنف: {item_number}")
    
    # 4. محاكاة عرض البيانات في الواجهة
    print("\n📺 4. محاكاة عرض البيانات في الواجهة...")
    
    all_items = AddedItem.get_all()
    workflow_items = [item for item in all_items if item.item_number.startswith('WORKFLOW')]
    
    print(f"📋 عدد أصناف سير العمل: {len(workflow_items)}")
    
    # محاكاة عرض الجدول
    print("\n📊 عرض الجدول:")
    print("الرقم | رقم الصنف | اسم الصنف | نوع العهدة | التصنيف | الكمية الحالية | الكمية المدخلة")
    print("-" * 100)
    
    for item in workflow_items:
        print(f"{getattr(item, 'id', '')} | {item.item_number} | {item.item_name} | {item.custody_type} | {item.classification} | {item.current_quantity} | {item.entered_quantity}")
    
    print("\n✅ اختبار سير العمل الكامل مكتمل!")

if __name__ == "__main__":
    test_complete_workflow()
