#!/usr/bin/env python3
"""
إنشاء ملف Excel تجريبي لاختبار الاستيراد
"""

import pandas as pd

# إنشاء البيانات التجريبية
data = {
    'الرقم': [1, 2, 3],
    'رقم الصنف': ['UI_001', 'UI_002', 'UI_003'],
    'اسم الصنف': ['اختبار واجهة 1', 'اختبار واجهة 2', 'اختبار واجهة 3'],
    'نوع العهدة': ['مستهلكة', 'مستديمة', 'مستهلكة'],
    'التصنيف': ['اختبار', 'اختبار', 'اختبار'],
    'الوحدة': ['عدد', 'عدد', 'عدد'],
    'الكمية الحالية': [100, 200, 300],
    'الكمية المدخلة': [100, 200, 300]
}

# إنشاء DataFrame
df = pd.DataFrame(data)

# حفظ الملف
df.to_excel('test_excel_import_ui.xlsx', index=False, engine='openpyxl')

print("✅ تم إنشاء ملف Excel التجريبي: test_excel_import_ui.xlsx")
print("📋 البيانات:")
print(df.to_string(index=False))
