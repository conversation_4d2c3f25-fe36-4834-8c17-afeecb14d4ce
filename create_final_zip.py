#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إنشاء ملف zip نهائي للمجلد الكامل
Create final zip file for the complete folder
"""

import os
import zipfile
import time
from pathlib import Path


def create_final_zip():
    """إنشاء ملف zip نهائي للمجلد الكامل"""
    try:
        dist_dir = Path('dist')
        final_dir = dist_dir / "نظام_إدارة_المخازن_كامل"
        
        if not final_dir.exists():
            print("❌ المجلد النهائي غير موجود")
            return False
        
        # إنشاء اسم ملف zip
        timestamp = time.strftime('%Y%m%d_%H%M%S')
        zip_name = f"نظام_إدارة_المخازن_كامل_{timestamp}.zip"
        zip_path = dist_dir / zip_name
        
        print(f"📦 إنشاء ملف zip نهائي: {zip_name}")
        print("=" * 50)
        
        # إنشاء ملف zip مع ضغط عالي
        with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED, compresslevel=9) as zipf:
            for root, dirs, files in os.walk(final_dir):
                for file in files:
                    file_path = Path(root) / file
                    # إنشاء مسار نسبي يبدأ من اسم المجلد
                    arc_name = Path("نظام_إدارة_المخازن_كامل") / file_path.relative_to(final_dir)
                    
                    zipf.write(file_path, arc_name)
                    
                    # عرض تقدم العملية
                    file_size = file_path.stat().st_size
                    if file_size > 1024 * 1024:  # أكبر من 1 MB
                        size_mb = file_size / (1024 * 1024)
                        print(f"  ✅ إضافة: {file} ({size_mb:.1f} MB)")
                    else:
                        print(f"  ✅ إضافة: {file}")
        
        # حساب أحجام الملفات
        original_size = sum(f.stat().st_size for f in final_dir.rglob('*') if f.is_file())
        compressed_size = zip_path.stat().st_size
        compression_ratio = (1 - compressed_size / original_size) * 100
        
        print("\n" + "=" * 50)
        print("✅ تم إنشاء الملف المضغوط بنجاح!")
        print(f"📁 المسار: {zip_path}")
        print(f"📊 الحجم الأصلي: {original_size / (1024 * 1024):.1f} MB")
        print(f"📊 الحجم المضغوط: {compressed_size / (1024 * 1024):.1f} MB")
        print(f"📊 نسبة الضغط: {compression_ratio:.1f}%")
        
        # إنشاء ملف معلومات الإصدار
        create_version_info(dist_dir, zip_name, original_size, compressed_size)
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء الملف المضغوط: {e}")
        return False


def create_version_info(dist_dir, zip_name, original_size, compressed_size):
    """إنشاء ملف معلومات الإصدار"""
    try:
        version_info = f"""
# نظام إدارة المخازن والمستودعات
## معلومات الإصدار النهائي

### تفاصيل الإصدار:
- **اسم الملف**: {zip_name}
- **تاريخ الإنشاء**: {time.strftime('%Y-%m-%d %H:%M:%S')}
- **إصدار البرنامج**: 2.0
- **نوع الإصدار**: إصدار كامل ونهائي

### معلومات الحجم:
- **الحجم الأصلي**: {original_size / (1024 * 1024):.1f} MB
- **الحجم المضغوط**: {compressed_size / (1024 * 1024):.1f} MB
- **نسبة الضغط**: {(1 - compressed_size / original_size) * 100:.1f}%

### محتويات الحزمة:
1. **نظام_إدارة_المخازن_مستقل.exe** - ملف تنفيذي مستقل (48+ MB)
2. **نظام_إدارة_المخازن_محمول.zip** - حزمة محمولة (47+ MB)
3. **README.txt** - دليل شامل للبرنامج
4. **دليل_التشغيل_السريع.txt** - خطوات التشغيل السريع
5. **معلومات_التثبيت.txt** - تفاصيل التثبيت
6. **معلومات_التوزيع.txt** - معلومات التوزيع

### طريقة الاستخدام:
1. استخرج الملف المضغوط إلى أي مجلد
2. ادخل إلى مجلد "نظام_إدارة_المخازن_كامل"
3. اختر طريقة التشغيل المناسبة:
   - للتشغيل المباشر: انقر على "نظام_إدارة_المخازن_مستقل.exe"
   - للنسخة المحمولة: استخرج "نظام_إدارة_المخازن_محمول.zip"

### بيانات تسجيل الدخول:
- **اسم المستخدم**: admin
- **كلمة المرور**: admin

### المتطلبات:
- نظام التشغيل: Windows 10/11
- الذاكرة: 4 GB RAM (مُوصى به)
- مساحة القرص: 200 MB للاستخراج والتشغيل

### الميزات الرئيسية:
✅ إدارة شاملة للمخازن والمستودعات
✅ إدارة الأصناف والمواد مع تتبع الكميات
✅ إدارة المستفيدين والهيكل التنظيمي
✅ معاملات الصرف والاستلام
✅ تقارير مفصلة وإحصائيات
✅ نظام نسخ احتياطية تلقائي
✅ واجهة عربية كاملة مع دعم RTL
✅ استيراد وتصدير Excel
✅ طباعة التقارير والإيصالات

### ملاحظات مهمة:
- هذا إصدار كامل ومستقل لا يحتاج تثبيت مكتبات إضافية
- يُنصح بتشغيل البرنامج كمسؤول للحصول على أفضل أداء
- احتفظ بنسخة احتياطية من البيانات دورياً
- راجع ملفات التوثيق المرفقة للحصول على المساعدة

### الدعم الفني:
للحصول على الدعم الفني أو الإبلاغ عن مشاكل، يرجى التواصل مع فريق التطوير.

---
© 2025 - نظام إدارة المخازن والمستودعات
جميع الحقوق محفوظة
"""
        
        version_file = dist_dir / 'معلومات_الإصدار_النهائي.txt'
        with open(version_file, 'w', encoding='utf-8') as f:
            f.write(version_info)
        
        print("✅ تم إنشاء ملف معلومات الإصدار")
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء ملف معلومات الإصدار: {e}")


def show_final_summary():
    """عرض ملخص نهائي"""
    dist_dir = Path('dist')
    
    print("\n" + "=" * 60)
    print("🎉 تم إنشاء الإصدار النهائي بنجاح!")
    print("=" * 60)
    
    print(f"\n📁 محتويات مجلد dist:")
    
    items = list(dist_dir.iterdir())
    for item in sorted(items):
        if item.is_file():
            size_mb = item.stat().st_size / (1024 * 1024)
            print(f"  📄 {item.name} ({size_mb:.1f} MB)")
        else:
            print(f"  📁 {item.name}/")
    
    print("\n" + "=" * 60)
    print("✨ البرنامج جاهز للتوزيع النهائي!")
    print("📦 يمكنك توزيع الملف المضغوط أو المجلد مباشرة")
    print("🚀 جميع المكتبات والملفات مدمجة ومتوفرة")
    print("=" * 60)


def main():
    """الدالة الرئيسية"""
    print("📦 إنشاء ملف zip نهائي للبرنامج الكامل")
    print("=" * 50)
    
    success = create_final_zip()
    
    if success:
        show_final_summary()
        print("\n🎯 تم إنشاء الإصدار النهائي بنجاح!")
    else:
        print("\n💥 فشل في إنشاء الإصدار النهائي!")
    
    return success


if __name__ == "__main__":
    success = main()
    
    if success:
        print("\n✅ العملية مكتملة!")
        print("🎉 نظام إدارة المخازن جاهز للتوزيع والاستخدام!")
    else:
        print("\n❌ فشلت العملية!")
    
    input("\n⏸️ اضغط Enter للخروج...")
