#!/usr/bin/env python3
"""
إنشاء ملف Excel تجريبي لاختبار استيراد الأصناف
"""

import pandas as pd
import os

def create_test_excel():
    """إنشاء ملف Excel تجريبي للاستيراد"""
    
    # البيانات التجريبية بالتنسيق الجديد - مع كميات مختلفة للاختبار
    test_data = {
        'الرقم': [1, 2, 3, 4, 5],
        'رقم الصنف': ['TEST001', 'TEST002', 'TEST003', 'TEST004', 'TEST005'],
        'اسم الصنف': ['قلم رصاص اختبار', 'دفتر A4 اختبار', 'مسطرة 30 سم اختبار', 'ممحاة اختبار', 'مقص اختبار'],
        'نوع العهدة': ['مستهلكة', 'مستديمة', 'مستهلكة', 'مستهلكة', 'مستديمة'],
        'التصنيف': ['قرطاسية', 'مكتبية', 'قرطاسية', 'قرطاسية', 'مكتبية'],
        'الوحدة': ['عدد', 'عدد', 'عدد', 'عدد', 'عدد'],
        'الكمية الحالية': [150, 80, 35, 95, 60],  # كميات مختلفة للاختبار
        'الكمية المدخلة': [150, 80, 35, 95, 60]   # نفس الكميات
    }
    
    # إنشاء DataFrame
    df = pd.DataFrame(test_data)
    
    # حفظ الملف
    file_path = 'test_items_import.xlsx'
    df.to_excel(file_path, index=False, engine='openpyxl')
    
    print(f"✅ تم إنشاء ملف الاختبار: {file_path}")
    print(f"📊 عدد الأصناف: {len(df)}")
    print(f"📋 الأعمدة: {list(df.columns)}")
    
    return file_path

if __name__ == "__main__":
    create_test_excel()
