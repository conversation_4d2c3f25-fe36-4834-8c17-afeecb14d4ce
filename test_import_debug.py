#!/usr/bin/env python3
"""
اختبار استيراد Excel مع تتبع مفصل للبيانات
"""

import pandas as pd
import sys
from pathlib import Path

# إضافة مسار المشروع
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from models import AddedItem
from datetime import datetime

def test_excel_import():
    """اختبار استيراد Excel مع تتبع البيانات"""
    
    print("🔍 بدء اختبار استيراد Excel...")
    
    # قراءة ملف Excel
    file_path = 'test_items_import.xlsx'
    try:
        df = pd.read_excel(file_path, engine='openpyxl')
        print(f"✅ تم قراءة الملف بنجاح: {len(df)} صف")
        print(f"📋 الأعمدة: {list(df.columns)}")
        
        # عرض البيانات
        print("\n📊 البيانات المقروءة:")
        for index, row in df.iterrows():
            print(f"\n--- الصف {index + 1} ---")
            for col in df.columns:
                value = row[col]
                print(f"  {col}: {value} (نوع: {type(value)})")
            
            # اختبار معالجة الكميات
            current_qty_raw = row.get('الكمية الحالية', 0)
            entered_qty_raw = row.get('الكمية المدخلة', 0)
            
            print(f"\n🔢 معالجة الكميات:")
            print(f"  الكمية الحالية الخام: {current_qty_raw} (نوع: {type(current_qty_raw)})")
            print(f"  pd.isna(الكمية الحالية): {pd.isna(current_qty_raw)}")
            
            # تطبيق نفس المنطق المستخدم في الكود
            current_qty = int(row.get('الكمية الحالية', 0)) if not pd.isna(row.get('الكمية الحالية', 0)) else 0
            entered_qty = int(row.get('الكمية المدخلة', 0)) if not pd.isna(row.get('الكمية المدخلة', 0)) else 0
            
            print(f"  الكمية الحالية المعالجة: {current_qty}")
            print(f"  الكمية المدخلة المعالجة: {entered_qty}")
            
            # إنشاء كائن AddedItem للاختبار
            test_item = AddedItem(
                item_number=str(row['رقم الصنف']),
                item_name=str(row['اسم الصنف']),
                custody_type=str(row.get('نوع العهدة', '')),
                classification=str(row.get('التصنيف', '')),
                unit=str(row.get('الوحدة', 'عدد')),
                current_quantity=current_qty,
                entered_quantity=entered_qty,
                data_entry_user="اختبار",
                entry_date=datetime.now().strftime('%Y-%m-%d'),
                is_active=True
            )
            
            print(f"\n📦 كائن AddedItem:")
            print(f"  رقم الصنف: {test_item.item_number}")
            print(f"  اسم الصنف: {test_item.item_name}")
            print(f"  الكمية الحالية: {test_item.current_quantity}")
            print(f"  الكمية المدخلة: {test_item.entered_quantity}")
            
            # اختبار الحفظ (بدون حفظ فعلي)
            print(f"  سيتم حفظ: current_quantity={test_item.current_quantity}, entered_quantity={test_item.entered_quantity}")
            
    except Exception as e:
        print(f"❌ خطأ في قراءة الملف: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_excel_import()
