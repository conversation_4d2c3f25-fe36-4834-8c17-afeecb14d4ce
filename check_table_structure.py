#!/usr/bin/env python3
"""
فحص بنية جدول added_items
"""

import sys
from pathlib import Path

# إضافة مسار المشروع
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from database import db_manager

def check_table_structure():
    """فحص بنية جدول added_items"""
    
    print("🔍 فحص بنية جدول added_items...")
    
    try:
        # فحص بنية الجدول
        columns = db_manager.fetch_all("PRAGMA table_info(added_items)")
        print(f"\n📋 أعمدة الجدول ({len(columns)} عمود):")
        for i, col in enumerate(columns):
            print(f"  {i}: {col[1]} ({col[2]}) - افتراضي: {col[4]} - NOT NULL: {col[3]}")
        
        # فحص البيانات الخام
        print(f"\n📊 البيانات الخام من الجدول:")
        raw_data = db_manager.fetch_all("SELECT * FROM added_items LIMIT 3")
        for i, row in enumerate(raw_data):
            print(f"\n--- الصف {i+1} ---")
            for j, value in enumerate(row):
                col_name = columns[j][1] if j < len(columns) else f"col_{j}"
                print(f"  {j}: {col_name} = {value}")
                
        # فحص استعلام محدد للكميات
        print(f"\n🔢 فحص الكميات فقط:")
        quantity_data = db_manager.fetch_all("""
            SELECT item_number, item_name, current_quantity, entered_quantity, data_entry_user 
            FROM added_items 
            LIMIT 5
        """)
        for row in quantity_data:
            print(f"  {row[0]} | {row[1]} | كمية حالية: {row[2]} | كمية مدخلة: {row[3]} | مستخدم: {row[4]}")
            
    except Exception as e:
        print(f"❌ خطأ في فحص بنية الجدول: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    check_table_structure()
